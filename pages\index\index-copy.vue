<template>
	<view :data-theme="theme" id="home">
		<tui-skeleton v-if="showSkeleton"></tui-skeleton>
		<view class="page-index tui-skeleton page_count" :class="{'bgf':navIndex >0}"
			:style="{visibility: showSkeleton ? 'hidden' : 'visible'}">
			<view class="page_count">
				<!--bg-->
				<view class="bg-img">
					<img :src="bgColor" alt="">
				</view>
				<!--logo-->
				<view :class="{scrolled:isScrolled, 'my-main': true}">
					<!-- #ifdef H5 -->
					<view class="h5-header" :style="'background: '+ bgColor +';margin-top:'+ 0 +'rpx;'">
						<view class="header flex">
							<view class="logo">
								<image :src="logoUrl" mode=""></image>
							</view>
						</view>
					</view>
					<!-- #endif -->
					<!-- #ifdef MP || APP-PLUS -->
					<view class="mp-header" :class="{scrolled:isScrolled}">
						<view class="sys-head" :style="{height: statusBarHeight}"></view>
						<view style="height: 80rpx;margin-right: 4rpx;">
							<view class="header flex">
								<view class="tui-skeleton-rect">
									<image :src="logoUrl" mode=""></image>
								</view>
								<!-- <view class="user-location">
									<text class="iconfont icon-dingwei"></text>
									<text class="city" v-if="userAddress">{{userAddress.city}}{{userAddress.district}}</text>
								</view> -->
								<view class="mer-list" v-if="merList.length > 0" @click="openMerNav">
									<view class="mer-name">{{merList[0].name}}</view>
									<view class="mer-xiala iconfont icon-xiala"></view>
								</view>
								<button class="mer-home" hover-class="none" @click="goMerHome">进店</button>
							</view>
						</view>
					</view>
					<!-- #endif -->

					<view class='search-box acea-row row-center-wrapper' :class="{scrolled:isScrolled}">
						<navigator url="/pages/goods/goods_search/index" class="tui-skeleton-rect" hover-class="none"><text
								class="iconfont icon-xiazai5 tui-skeleton-rect"></text>
							搜索商品</navigator>
					</view>

					<!-- 分类导航 -->
					<!-- <tabNav @bindHeight="bindHeight" @changeTab="changeTab" :isScrolled="isScrolled"
						:navIndex="navIndex">
					</tabNav> -->
				</view>

				<view class="mer-logo" :style="{marginTop: '180rpx', paddingTop: statusBarHeight}">
					<image :src='bgColor' class="skeleton-radius" @click="goMerHome"></image>
				</view>
				<!-- banner -->
				<!-- <view class="swiperBg" :style="{marginTop: swiperTop+'rpx',paddingTop: statusBarHeight}">
					<view class="swiper page_swiper" v-if="banner.length && navIndex === 0">
						<swiper :autoplay="true" :circular="circular" :interval="interval" :duration="duration"
							previous-margin="30rpx" next-margin="30rpx" indicator-color="rgba(255,255,255,0.6)"
							indicator-active-color="#fff" :current="swiperCur" @change="swiperChange">
							<block v-for="(item,index) in banner" :key="index">
								<swiper-item :class="{ active: index == swiperCur }" class="scalex">
									<view @click="menusTap(item.url)"
										class='slide-navigator acea-row row-between-wrapper tui-skeleton-rect'>
										<image :src="item.pic" class="slide-image aa"></image>
									</view>
								</swiper-item>
							</block>
						</swiper>
						<view class="dots">
							<block v-for="(item,index) in banner" :key="index">
								<view class="dot" :class="index == swiperCur ? ' active' : ''"></view>
							</block>
						</view>
					</view>
				</view> -->

			</view>

			<!-- 首页展示 -->
			<view class="page_content skeleton">
				<view id="pageIndex" class="pageIndex" v-if="navIndex === 0">
					<!-- news -->
					<!-- <view v-if="headline.length > 0" class='notice acea-row row-middle row-between'
						@click.native="bindEdit('indexNews')">
						<view class="pic" v-if="imgHost" @click="toNewsList()"></view>
						<text class='line'>|</text>
						<view class='swipers'>
							<swiper :indicator-dots="indicatorDots" :autoplay="true" interval="2500" duration="500"
								vertical="true" circular="true">
								<block v-for="(item,index) in headline" :key='index'>
									<swiper-item @touchmove.stop="stopTouchMove">
										<view class='item tui-skeleton-rect'
											@click="menusTap('/pages/goods/news_details/index?id='+item.id)">
											<view class='line1'>{{item.title}}</view>
										</view>
									</swiper-item>
								</block>
							</swiper>
						</view>
						<view class="iconfont icon-xiangyou" @click="toNewsList()"></view>
					</view> -->

					<!-- menu -->
					<view class='nav acea-row' @click.native="bindEdit('indexMenu')">
						<block v-for="(item,index) in menus" :key="index">
							<view class='item' @click="menusTap(item.url)">
								<view class='pictrue tui-skeleton-rect'>
									<image :src='item.pic' class="skeleton-radius"></image>
								</view>
								<view class="menu-txt">{{item.name}}</view>
							</view>
						</block>
					</view>
					<!-- <merchant-list v-if="shopStreetSwitch && userAddress" :userAddress="userAddress"></merchant-list> -->
				</view>
				<!-- 第三级分类 -->
				<!-- <view class="productList" v-if="navIndex > 0 && sortList.length > 0">
					<view class="sort acea-row" :class="sortList.length ? '' : 'no_pad'"
						:style="{ marginTop: sortMarTop + 'px' }">
						<navigator hover-class="none"
							:url="'/pages/goods/goods_list/index?cid=' + item.id + '&title=' + item.name" class="item"
							v-for="(item, index) in sortList" :key="index">
							<view class="pictrue">
								<image :src="item.icon" class='slide-image tui-skeleton-rect'></image>
							</view>
							<view class="text">{{ item.name }}</view>
						</navigator>
						<view class="item" @click="bindMore()" v-if="sortList.length >= 9">
							<view class="pictrues acea-row row-center-wrapper"><text
									class="iconfont icon-gengduo2"></text></view>
							<view class="text">更多</view>
						</view>
					</view>
				</view> -->
				<recommend ref="recommendIndex" :categoryId='categoryId' :isShowTitle="isShowTitle" :isShowLoadMore="true"
					@noCommodity="noCommodity"></recommend>
				<view class='noCommodity' v-if="isNoCommodity && navIndex > 0">
					<view class='pictrue'>
						<image src='@/static/images/noShopper.png'></image>
					</view>
					<text class="text">暂无商品</text>
				</view>
			</view>

			<view class="dialog-mer-nav" v-show="changingCurMer" :style="{ top: '80rpx', marginTop: statusBarHeight}">
				<view class="dialog-mer-nav-item" v-for="(item, index) in merList" :key="index" @click="changeCurMer(item.id)">
					<view class="mer-icon">
						<image :src="item.avatarImage" mode=""></image>
					</view>
					<view class="mer-name">{{item.name}}</view>
				</view>
			</view>
			<view class="dialog-mer-mask" v-if="changingCurMer" @touchmove="hideMerNav" @click="hideMerNav()"></view>

			<aTip :isCustom="true" :text="wxText" :borderR="5"></aTip>
			<atModel v-if="locationStatus" :locationType="true" @closeModel="modelCancel" @confirmModel="confirmModel"
				:content="locationContent"></atModel>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import Auth from '@/libs/wechat';
	import Cache from '../../utils/cache';
	import tuiSkeleton from '@/components/base/tui-skeleton.vue';
	var statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px';
	let app = getApp();
	import {
		getIndexData,
		setCouponReceive,
		getTheme,
		getAppVersion,
		getCategoryThird
	} from '@/api/api.js';
	import {
		spread
	} from "@/api/user";
	// #ifdef MP-WEIXIN || APP-PLUS
	import {
		getTemlIds
	} from '@/api/api.js';
	// #endif
	// #ifdef H5  
	import {
		follow
	} from '@/api/public.js';
	// #endif
	import {
		getShare
	} from '@/api/public.js';
	import {
		indexMerListApi,
		getMerCollectListApi,
		getMerCollectAddApi
	} from '@/api/merchant.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import atModel from '@/components/accredit/index.vue'
	import ClipboardJS from "@/plugin/clipboard/clipboard.js";
	import qqmapwx from "@/plugin/qqmap-wx-jssdk1.2/qqmap-wx-jssdk.min.js";
	import aTip from '@/components/add-tips/index.vue';
	import {
		mapGetters
	} from "vuex";
	import {
		getCategoryList,
		getGroomList
	} from '@/api/product.js';
	import {
		autoFollow
	} from '@/libs/merchant.js';
	import {
		silenceBindingSpread,
		getCityList
	} from '@/utils/index.js';
	import animationType from '@/utils/animationType.js'
	import merchantList from './components/merchant.vue'
	import tabNav from './components/tabNav';
	import recommend from "@/components/base/recommend.vue";
	const arrTemp = ["beforePay", "afterPay", "createBargain", "pink"];
	export default {
		computed: mapGetters(['isLogin', 'uid', 'currentMerId', 'globalData', 'userAddress']),
		components: {
			aTip,
			atModel,
			merchantList,
			tabNav,
			recommend,
			tuiSkeleton
		},
		data() {
			return {
				isNoCommodity: false,
				swiperTop: 0,
				isScrolled: false,
				categoryId: 0,
				showSkeleton: true, //骨架屏显示隐藏
				isNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取
				loaded: false,
				loading: false,
				isAuto: false, //没有授权的不会自动授权
				isShowAuth: false, //是否隐藏授权
				statusBarHeight: statusBarHeight,
				navIndex: 0,
				followUrl: "",
				followHid: true,
				followCode: false,
				logoUrl: "",
				banner: [{
					url: '',
					pic: '',
					id: '',
					name: ''
				}],
				menus: new Array(10).fill('').map(() => {
					return {
						url: '',
						pic: '',
						id: '',
						name: ''
					}
				}),
				indicatorDots: false,
				circular: true,
				autoplay: false,
				interval: 3000,
				duration: 500,
				window: false,
				navH: "",
				ProductNavindex: 0,
				marTop: 0,
				sortProduct: [],
				hotPage: 1,
				hotLimit: 10,
				hotScroll: false,
				swiperCur: 0,
				explosiveMoney: new Array(4).fill('').map(() => {
					return {
						title: ''
					}
				}),
				searchH: 0,
				goodType: 0, //精品推荐Type
				headline: [], // 新闻简报
				site_name: '', //首页title
				configApi: {}, //分享类容配置
				tabsScrollLeft: 0, // tabs当前偏移量
				scrollLeft: 0,
				listActive: 0, // 当前选中项
				// duration: 2 // 下划线动画时长
				theme: app.globalData.theme,
				imgHost: '',
				picBg: '@/static/images/new_header.png',
				appUpdate: {},
				wxText: "点击添加到我的小程序，微信首页下拉即可访问商城。",
				locationContent: '授权位置信息，提供完整服务',
				locationStatus: false,
				sortMpTop: 0,
				// #ifdef APP-PLUS || MP
				isFixed: true,
				// #endif
				// #ifdef H5
				isFixed: false,
				// #endif
				domOffsetTop: 50,
				prodeuctTop: 30,
				sortList: [],
				sortMarTop: 0,
				componentKey: 0,
				isShowTitle: true,
				shopStreetSwitch: true,
				bgColor: '',
				navHeight: 38,
				domHeight: 0,
				cateNavActive: 0,
				merList: [],
				changingCurMer: false
			}
		},
		watch: {
			ProductNavindex(newVal) { // 监听当前选中项
				this.setTabList()
			},
			listActive(newVal) { // 监听当前选中项
				this.setTabList()
			},
			isLogin() {
				this.getMerList();
			},
			currentMerId(newVal, oldVal) {
				if (newVal == oldVal) return;
				this.getMerList();
			}
		},
		created() {},
		onLaunch: function() {
			//this.isNodes++;
		},
		mounted() {
			this.setTabList();
		},
		onLoad(options) {
			if (this.globalData.isIframe) {
				setTimeout(() => {
					let active;
					document.getElementById('pageIndex').children.forEach(dom => {
						dom.addEventListener('click', (e) => {
							e.stopPropagation();
							e.preventDefault();
							if (dom === active) return;
							dom.classList.add('borderShow');
							active && active.classList.remove('borderShow');
							active = dom;
						})
					})
				});
			}
			setTimeout(() => {
				this.isNodes++;
			}, 100);

			// #ifdef APP-PLUS
			this.appVersionConfig(); //APP版本检测
			// #endif
			// #ifdef MP
			// 获取小程序头部高度
			this.marTop = (uni.getSystemInfoSync().statusBarHeight + 40);
			// #endif
			// #ifndef MP || APP-PLUS
			//this.navH = 0;
			// #endif
			this.getIndexConfig();
			// #ifdef MP || APP-PLUS
			this.getTemlIds()
			// #endif

			if (options.spread) {
				app.globalData.spread = options.spread;
				silenceBindingSpread({
					spread: app.globalData.spread
				}, this.isLogin);
			}
			if (options.mid) {
				app.globalData.mid = options.mid;
				autoFollow(app.globalData.mid).then(() => {
					this.$store.commit('CURRENT_MERID', app.globalData.mid);
				}).catch(e => {});
			}
			if (options.scene) {
				// 小程序扫码进入
				let value = this.$util.getUrlParams(decodeURIComponent(options.scene));
				let scene = this.$util.formatMpQrCodeData(value);
				if (scene.spread) {
					app.globalData.spread = scene.spread;
					silenceBindingSpread({
						spread: app.globalData.spread
					}, this.isLogin);
				}
				if (scene.mid) {
					app.globalData.mid = scene.mid;
					autoFollow(app.globalData.mid).then(() => {
						this.$store.commit('CURRENT_MERID', app.globalData.mid);
					}).catch(e => {});
				}
			}
			this.getMerList();

			// if (uni.getStorageSync('user_latitude') && uni.getStorageSync('user_longitude')) {
			// 	let that = this;
			// 	uni.getLocation({
			// 		type: 'gcj02',
			// 		altitude: true,
			// 		geocode: true,
			// 		success: (res) => {
			// 			try {
			// 				uni.setStorageSync('user_latitude', res.latitude);
			// 				uni.setStorageSync('user_longitude', res.longitude);

			// 				const qqmapwxsdk = new qqmapwx({
			// 					key: "H4UBZ-MXWLU-QZRVW-B5MQW-WY647-I4BSX"
			// 				});
			// 				qqmapwxsdk.reverseGeocoder({
			// 					location:{
			// 						latitude: res.latitude,
			// 						longitude: res.longitude
			// 					},
			// 					success: (res) => {
			// 						if (res.status == 0) {
			// 							that.$store.commit('UPDATE_USER_ADDRESSS', res.result.ad_info)
			// 						}
			// 					},
			// 					fail: (res) => {
			// 						console.log(res);
			// 					}
			// 				});
			// 			} catch {}
			// 		}
			// 	});
			// }
		},
		onShow() {
			let self = this;
			// #ifdef APP-PLUS
			let barHeight = uni.getSystemInfoSync().statusBarHeight;
			self.marTop = barHeight + 40; //刘海屏
			setTimeout(() => {
				if (self.appUpdate.openUpgrade == 'true') {
					self.appVersionConfig();
				}
			}, 1000)
			// #endif
			uni.showTabBar();
		},
		// 滚动监听
		onPageScroll(e) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			uni.$emit('scroll');
			if (e.scrollTop > this.domOffsetTop) {
				this.isScrolled = true;
				//this.isFixed = true;
			}
			if (e.scrollTop < this.domOffsetTop) {
				this.$nextTick(() => {
					//this.isFixed = false;
					this.isScrolled = false;
				});
			}
		},
		methods: {
			goMerHome() {
				if (this.currentMerId > 0) {
					uni.navigateTo({
						url: `/pages/merchant/home/<USER>
					})
				}
			},
			getMerList() {
				if (this.isLogin === false) {
					indexMerListApi({
						isRecommend: true
					}).then((res) => {
						if (res.data && res.data.length > 0) {
							let merList = [];
							res.data.forEach(mer => {
								merList.push({
									id: mer.id,
									name: mer.name,
									avatarImage: mer.avatar,
									backImage: mer.backImage
								});
							});
							this.$set(this, "merList", merList);
							this.$store.commit('CURRENT_MERID', merList[0].id);
							this.$set(this, "logoUrl", merList[0].avatarImage);
							this.$set(this, "bgColor", merList[0].backImage);
						}
					});
				} else {
					getMerCollectListApi({
						page: 1,
						limit: 20
					}).then(res => {
						if (res.data && res.data.list && res.data.list.length > 0) {
							let merList = [];
							res.data.list.forEach(mer => {
								merList.push({
									id: mer.merId,
									name: mer.merName,
									avatarImage: mer.merAvatar,
									backImage: mer.merBackImage
								});
							});
							this.$set(this, "merList", merList);
							this.$store.commit('CURRENT_MERID', merList[0].id);
							this.$set(this, "logoUrl", merList[0].avatarImage);
							this.$set(this, "bgColor", merList[0].backImage);
						} else {
							indexMerListApi({
								isRecommend: true
							}).then((res) => {
								if (res.data && res.data.length > 0) {
									let merList = [];
									res.data.forEach(mer => {
										merList.push({
											id: mer.id,
											name: mer.name,
											avatarImage: mer.avatar,
											backImage: mer.backImage
										});
									});
									this.$set(this, "merList", merList);
									this.$store.commit('CURRENT_MERID', merList[0].id);
									this.$set(this, "logoUrl", merList[0].avatarImage);
									this.$set(this, "bgColor", merList[0].backImage);
								}
							});
						}
					});
				}
			},
			openMerNav() {
				this.$set(this, "changingCurMer", true);
			},
			hideMerNav() {
				this.$set(this, "changingCurMer", false);
			},
			changeCurMer(merId) {
				this.hideMerNav();
				if (this.isLogin === false) {
					toLogin();
				} else {
					getMerCollectAddApi(merId).then(res => {
						this.getMerList();
					}).catch(err => {
						this.$util.Tips({
							title: err
						});
					});
				}
			},
			bindMore() {
				uni.setStorageSync('cateNavActive', this.cateNavActive);
				uni.setStorageSync('categoryId', this.categoryId);
				uni.switchTab({
					url: `/pages/goods_cate/index`
				})
			},
			noCommodity(e) {
				this.isNoCommodity = e == 0 ? true : false;
			},
			swiperChange(e) {
				let {
					current,
					source
				} = e.detail;
				if (source === 'autoplay' || source === 'touch') {
					this.swiperCur = e.detail.current;
					this.bgColor = this.banner[e.detail.current]['pic']
				}
			},
			bindHeight(data) {
				const query = uni.createSelectorQuery().in(this);
				// #ifdef H5
				query.select('.header').boundingClientRect(res => {
					this.marTop = res.height
				}).exec();
				// #endif
				// #ifdef MP || APP-PLUS
				query.select('.mp-header').boundingClientRect(res => {
					this.marTop = res.height
				}).exec();
				// #endif

				this.navHeight = data;
				// #ifdef APP-PLUS
				//this.sortMpTop = data.top + data.height;
				// #endif
				// #ifdef H5
				this.swiperTop = this.navHeight + this.marTop + (uni.getSystemInfoSync().statusBarHeight) + 44;
				//#endif
				// #ifndef H5
				this.swiperTop = 246;
				//#endif
			},
			// 导航分类切换
			changeTab(id, idx) {
				this.cateNavActive = idx;
				if (id && id > 0) {
					this.navIndex = 1;
					getCategoryThird(id).then(res => {
						this.sortList = res.data;
					});
					// #ifdef H5
					self.sortMarTop = 10;
					// #endif
					this.isShowTitle = false;
				} else {
					this.navIndex = 0;
					this.isShowTitle = true;
				}
				this.categoryId = id;
				this.componentKey += 1;
			},
			menusTap(url) {
				if (!url) return;
				if (url == '/pages/goods_cate/index') {
					uni.switchTab({
						url: url
					})
				} else {
					uni.navigateTo({
						animationType: animationType.type,
						animationDuration: animationType.duration,
						url: url
					})
				}
			},
			toNewsList() {
				uni.navigateTo({
					animationType: animationType.type,
					animationDuration: animationType.duration,
					url: '/pages/goods/news_list/index'
				})
			},
			bindEdit(name) {
				if (this.globalData.isIframe) {
					window.parent.postMessage({
							name: name
						},
						'*'
					);
					return;
				}
			},
			reloadData() {
				this.showSkeleton = false;
			},
			// scroll-view滑动事件
			scroll(e) {
				this.scrollLeft = e.detail.scrollLeft;
			},
			setTabList() {
				this.$nextTick(() => {
					this.scrollIntoView()
				})
			},
			// 计算tabs位置
			scrollIntoView() { // item滚动
				let lineLeft = 0;
				this.getElementData('#tab_list', (data) => {
					let list = data[0]
					this.getElementData(`#tab_item`, (data) => {
						let el = data[this.listActive]
						lineLeft = el.width / 2 + (-list.left) + el.left - list.width / 2 - this.scrollLeft
						this.tabsScrollLeft = this.scrollLeft + lineLeft
					})
				})
			},
			getElementData(el, callback) {
				uni.createSelectorQuery().in(this).selectAll(el).boundingClientRect().exec((data) => {
					callback(data[0]);
				});
			},
			xieyiApp() {
				uni.navigateTo({
					url: '/pages/users/web_page/index?webUel=https://admin.java.crmeb.net/useragreement/xieyi.html&title=协议内容'
				})
			},
			// #ifdef APP-PLUS
			xieyiApp() {
				uni.navigateTo({
					animationType: animationType.type,
					animationDuration: animationType.duration,
					url: '/pages/users/web_page/index?webUel=https://admin.java.crmeb.net/useragreement/xieyi.html&title=协议内容'
				})
			},
			// #endif
			// #ifdef MP || APP-PLUS
			getTemlIds() {
				getTemlIds().then(res => {
					if (res.data) {
						let tempIds = res.data.map((item) => {
							return item.tempId
						})
						wx.setStorageSync('wechatSubscribeTempID', tempIds);
					}
				});
				// for (var i in arrTemp) {
				// 	this.getTem(arrTemp[i]);
				// }
			},
			getTem(data) {
				getTemlIds({
					type: data
				}).then(res => {
					if (res.data) {
						let arr = res.data.map((item) => {
							return item.tempId
						})
						wx.setStorageSync('tempID' + data, arr);
					}
				});
			},
			// #endif
			// 关闭优惠券弹窗
			onColse() {
				this.$set(this, "window", false);
			},


			// 首页数据
			getIndexConfig: function() {
				let that = this;
				//this.isNodes++;
				getIndexData().then(res => {
					// that.$set(that, "logoUrl", res.data.logoUrl);
					let imgHost = res.data.logoUrl.split('crmebimage')[0];
					that.imgHost = imgHost;
					that.$Cache.set('imgHost', imgHost);
					that.$set(that, "banner", res.data.banner);
					// that.bgColor = that.banner[0].pic;
					that.$set(that, "menus", res.data.menus);
					that.$set(that, "headline", res.data.headline ? res.data.headline : []);
					if (res.data.shopStreetSwitch == "'1'") {
						that.$set(that, "shopStreetSwitch", true);
					} else {
						that.$set(that, "shopStreetSwitch", false);
					}
					// #ifdef H5 || APP-PLUS
					that.$store.commit("SET_CHATURL", res.data.yzfUrl);
					Cache.set('chatUrl', res.data.yzfUrl);
					// #endif
					Cache.setItem({
						name: 'platChatConfig',
						value: {
							servicePhone: res.data.consumerHotline, //客服电话
							serviceLink: res.data.consumerH5Url, //云智服
							serviceType: res.data.consumerType //客服类型四选一
						}
					});
					// this.getGroomList();
					// this.shareApi();
					this.reloadData();
					// #ifdef MP
					// if (!Cache.has('user_latitude')) {
					// 	this.locationStatus = true;
					// }
					// #endif

				});
			},
			appVersionConfig() {
				var that = this;
				//app升级
				// 获取本地应用资源版本号  
				getAppVersion().then(res => {
					that.$set(that.appUpdate, 'androidAddress', res.data.androidAddress);
					that.$set(that.appUpdate, 'appVersion', res.data.appVersion);
					that.$set(that.appUpdate, 'iosAddress', res.data.iosAddress);
					that.$set(that.appUpdate, 'openUpgrade', res.data.openUpgrade);
					plus.runtime.getProperty(plus.runtime.appid, function(inf) {
						let nowVersion = (inf.version).split('.').join('');
						let appVersion = (res.data.appVersion).split('.').join('');
						uni.getSystemInfo({
							success: (res) => {
								if (appVersion > nowVersion) {
									uni.showModal({
										title: '更新提示',
										content: '发现新版本，是否前去下载?',
										showCancel: that.appUpdate.openUpgrade ==
											'false' ? true : false,
										cancelColor: '#eeeeee',
										confirmColor: '#FF0000',
										success(response) {
											if (response.confirm) {
												switch (res.platform) {
													case "android":
														plus.runtime.openURL(that
															.appUpdate
															.androidAddress);
														break;
													case "ios":
														plus.runtime.openURL(encodeURI(
															that.appUpdate
															.iosAddress));
														break;
												}

											}
										}
									});
								}
							}
						})
					});
				})
			},
			shareApi: function() {
				getShare().then(res => {
					this.$set(this, 'configApi', res.data);
					this.$set(this, "site_name", res.data.title);
					uni.setNavigationBarTitle({
						title: this.site_name
					})
					// #ifdef H5
					this.setOpenShare(res.data);
					// #endif
				})
			},
			// 微信分享；
			setOpenShare: function(data) {
				let that = this;
				if (that.$wechat.isWeixin()) {
					let configAppMessage = {
						desc: data.synopsis,
						title: data.title,
						link: location.href,
						imgUrl: data.img
					};
					that.$wechat.wechatEvevt(["updateAppMessageShareData", "updateTimelineShareData"],
						configAppMessage);
				}
			},
			// 授权关闭
			authColse: function(e) {
				this.isShowAuth = e
			},
			stopTouchMove() {
				return true //禁止新闻swiper手动滑动
			},
			modelCancel() {
				this.locationStatus = false;
			},
			confirmModel() {
				let that = this;

				// uni.getLocation({
				// 	type: 'gcj02',
				// 	altitude: true,
				// 	geocode: true,
				// 	success: (res) => {
				// 		try {
				// 			uni.setStorageSync('user_latitude', res.latitude);
				// 			uni.setStorageSync('user_longitude', res.longitude);

				// 			const qqmapwxsdk = new qqmapwx({
				// 				key: "H4UBZ-MXWLU-QZRVW-B5MQW-WY647-I4BSX"
				// 			});
				// 			qqmapwxsdk.reverseGeocoder({
				// 				location:{
				// 					latitude: res.latitude,
				// 					longitude: res.longitude
				// 				},
				// 				success: (res) => {
				// 					if (res.status == 0) {
				// 						that.$store.commit('UPDATE_USER_ADDRESSS', res.result.ad_info)
				// 					}
				// 				},
				// 				fail: (res) => {
				// 					console.log(res);
				// 				}
				// 			});
				// 		} catch {}
				// 	}
				// });
				this.locationStatus = false;
			}
		},
		mounted() {
			let query = uni.createSelectorQuery().in(this);
			query.select("#home").boundingClientRect();
			query.exec(res => {
				this.domHeight = res[0].height;
			})
			let self = this;
			// #ifdef H5 || APP-PLUS
			// 获取H5 搜索框高度
			let appSearchH = uni.createSelectorQuery().select(".serch-wrapper");
			appSearchH.boundingClientRect(function(data) {
				self.searchH = data.height
			}).exec()
			// #endif
		},
		onReachBottom: function() {
			this.$refs.recommendIndex.get_host_product();
		},
		/**
		 * 用户点击右上角分享
		 */
		// #ifdef MP
		onShareAppMessage: function() {
			let spread = this.uid ? this.uid : 0;
			return {
				title: this.configApi.title,
				imageUrl: this.configApi.img,
				desc: this.configApi.synopsis,
				path: `/pages/index/index?spread=${spread}&mid=${this.currentMerId || ''}`,
			};
		}
		// #endif
	}
</script>
<style>
	page {
		height: auto;
		display: flex;
		flex-direction: column;
		height: 100%;
		/* #ifdef H5 */
		background-color: #fff;
		/* #endif */

	}
</style>
<style lang="scss" scoped>
	.swiperBg {
		z-index: 1;
		margin-top: 10rpx;

		.colorBg {
			position: absolute;
			left: 0;
			top: 0;
			height: 130rpx;
			width: 100%;
		}

		.page_swiper {
			position: relative;
			width: 100%;
			height: auto;
			margin: 0 auto;
			border-radius: 10rpx;
			overflow: hidden;
			z-index: 8;
			padding: 0 0rpx 20rpx 0rpx;

			swiper-item {
				border-radius: 10rpx;
			}

			.swiper-item,
			image,
			.acea-row.row-between-wrapper {
				width: 100%;
				height: 310rpx;
				margin: 0 auto;
				border-radius: 10rpx;
			}

			swiper {
				width: 100%;
				display: block;
				height: 310rpx;

				&.scalex {
					/deep/.uni-swiper-slide-frame {
						transform: translate(0, 0) !important;
					}
				}
			}

			image {
				transform: scale(0.96);
				transition: all 0.6s ease;
			}

			/deep/ swiper-item.active {
				image {
					transform: scale(1);
				}
			}

			/*用来包裹所有的小圆点  */
			.dots {
				width: 156rpx;
				height: 36rpx;
				display: flex;
				flex-direction: row;
				position: absolute;
				left: 320rpx;
				bottom: 6rpx;
			}

			/*未选中时的小圆点样式 */
			.dot {
				width: 16rpx;
				height: 6rpx;
				border-radius: 6rpx;
				margin-right: 6rpx;
				background-color: rgba(255, 255, 255, .4);

				/*选中以后的小圆点样式  */
				&.active {
					width: 32rpx;
					height: 6rpx;
					background-color: rgba(255, 255, 255, .4);
				}
			}
		}
	}

	.scrolled {
		width: 100%;
		background-color: #fff !important;
		color: #000 !important;

		.longItem,
		.click,
		.category text {
			color: #000 !important;
		}

		.btn .iconfont {
			color: #333 !important;
		}

		.iconnum {
			background: #333 !important;
		}

		.underline {
			background: #000 !important;
		}

		.click {
			&::after {
				background-color: #fff !important;
			}
		}

		.input,
		.uninput {
			background-color: #eee !important;
		}

		.user-location,
		.mer-list {
			color: #000 !important;
		}
	}

	.page_count {
		position: relative;
		overflow: hidden;

		.bg-img {
			position: absolute;
			width: 100%;
			height: 100%;
			top: 0;
			/* #ifdef MP || APP-PLUS */
			z-index: -1;
			/* #endif */
			/* #ifdef H5 */
			z-index: 0;
			/* #endif */
			z-index: 0;
			filter: blur(0);
			overflow: hidden;

			img {
				width: 100%;
				height: 100%;
				filter: blur(30rpx);
				transform: scale(1.5);
			}
		}
	}

	.my-main {
		left: 0;
		position: fixed;
		top: 0;
		width: 100%;
		z-index: 30;
		transition: background-color .5s ease;
	}

	.user-location {
		display: flex;
		align-items: center;
		color: #fff;

		.city {
			width: 300rpx;
			padding-left: 5rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			font-size: 26rpx;
		}
	}

	.search-box {
		width: 100%;
		height: 100rpx;
		font-size: 26rpx;
		color: #555;

		navigator {
			width: 700rpx;
			height: 60rpx;
			background-color: #f5f5f5;
			border-radius: 50rpx;
			box-sizing: border-box;
			padding: 0 25rpx;
			display: flex;
			align-items: center;
			margin-bottom: 15rpx;

			.iconfont {
				margin-right: 10rpx;
			}
		}
	}

	.icon-gengduo1 {
		color: #F8F8F8;
	}

	.pageIndex {
		padding: 0 24rpx;
	}

	.productList {
		background-color: #F5F5F5;

		// min-height: 70vh;
		.sort {
			width: 710rpx;
			max-height: 380rpx;
			background: rgba(255, 255, 255, 1);
			border-radius: 16rpx;
			padding: 0rpx 0rpx 20rpx 0rpx !important;
			flex-wrap: wrap;
			margin: 25rpx auto 0 auto;

			&.no_pad {
				padding: 0;
			}

			.item {
				width: 20%;
				margin-top: 20rpx;
				text-align: center;

				.pictrues {
					width: 90rpx;
					height: 90rpx;
					background: #F5F5F5;
					border-radius: 50%;
					margin: 0 auto;
				}

				.pictrue {
					width: 90rpx;
					height: 90rpx;
					background: #F5F5F5;
					border-radius: 50%;
					margin: 0 auto;
				}

				.slide-image {
					width: 90rpx;
					height: 90rpx;
					border-radius: 50%;
					overflow: hidden;
				}

				/deep/.easy-loadimage,
				uni-image,
				.easy-loadimage {
					width: 90rpx;
					height: 90rpx;
					display: inline-block;
				}

				.text {
					color: #272727;
					font-size: 24rpx;
					margin-top: 10rpx;
					// overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}
	}

	.productList .list {
		padding: 0 20rpx;
	}

	.productList .list.on {
		background-color: #fff;
		border-top: 1px solid #f6f6f6;
	}

	.productList .list .item {
		width: 345rpx;
		margin-top: 20rpx;
		background-color: #fff;
		border-radius: 10rpx;

		.name {
			display: flex;
			align-items: center;

			.name_text {
				display: inline-block;
				max-width: 200rpx;
			}
		}
	}

	.notice {
		width: 100%;
		height: 70rpx;
		border-radius: 10rpx;
		background-color: #fff;
		margin-bottom: 20rpx;
		line-height: 70rpx;
		padding: 0 20rpx;

		.line {
			color: #CCCCCC;
		}

		.pic {
			width: 130rpx;
			height: 36rpx;
			background-image: url('@/static/images/new_header.png');
			background-size: 100%;
			// @include index_new_img(theme);

			image {
				width: 100%;
				height: 100%;
				display: block !important;
			}
		}

		.swipers {
			height: 100%;
			width: 444rpx;
			overflow: hidden;

			swiper {
				height: 100%;
				width: 100%;
				overflow: hidden;
				font-size: 22rpx;
				color: #333333;
			}
		}

		.iconfont {
			color: #999999;
			font-size: 20rpx;
		}
	}

	.sticky-box {
		/* #ifndef APP-PLUS-NVUE */
		display: flex;
		position: -webkit-sticky;
		/* #endif */
		position: sticky;
		/* #ifdef H5*/
		top: var(--window-top);
		/* #endif */

		z-index: 99;
		flex-direction: row;
		margin: 0px;
		background: #f5f5f5;
		padding: 30rpx 0;
		/* #ifdef MP || APP-PLUS*/
		//top: 110rpx;
		/* #endif */
	}

	.tab {
		position: relative;
		display: flex;
		font-size: 28rpx;
		white-space: nowrap;

		&__item {
			flex: 1;
			padding: 0 20rpx;
			text-align: center;
			height: 60rpx;
			line-height: 60rpx;
			color: #666;

			&.active {
				color: #09C2C9;
			}
		}
	}

	.scroll-view_H {
		white-space: nowrap;
		width: 100%;
	}

	.privacy-wrapper {
		z-index: 999;
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background: #7F7F7F;

		.privacy-box {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			width: 560rpx;
			padding: 50rpx 45rpx 0;
			background: #fff;
			border-radius: 20rpx;

			.title {
				text-align: center;
				font-size: 32rpx;
				text-align: center;
				color: #333;
				font-weight: 700;
			}

			.content {
				margin-top: 20rpx;
				line-height: 1.5;
				font-size: 26rpx;
				color: #666;
				text-indent: 54rpx;

				i {
					font-style: normal;
					color: $theme-color;
				}
			}

			.btn-box {
				margin-top: 40rpx;
				text-align: center;
				font-size: 30rpx;

				.btn-item {
					height: 82rpx;
					line-height: 82rpx;
					background: linear-gradient(90deg, #F67A38 0%, #F11B09 100%);
					color: #fff;
					border-radius: 41rpx;
				}

				.btn {
					padding: 30rpx 0;
				}
			}
		}
	}

	.page-index {
		display: flex;
		flex-direction: column;
		min-height: 100%;
		// padding-bottom: 130rpx;
		// background: linear-gradient(180deg, #fff 0%, #f5f5f5 100%);

		.h5-header {
			width: 100%;
			padding: 24rpx;
			//@include main_bg_color(theme)

			.header {
				align-items: center;


				.logo {
					width: 118rpx;
					height: 42rpx;
					margin-right: 24rpx;
				}

				image {
					width: 118rpx;
					height: 42rpx;
				}
			}

			.tabNav {
				padding-top: 24rpx;
			}
		}

		/* #ifdef MP || APP-PLUS */
		.mp-header {
			width: 100%;
			/* #ifdef H5 */
			padding-bottom: 20rpx;
			/* #endif */

			.header {
				height: 100%;
				align-items: center;
				padding: 0 20rpx 0 20rpx;

				image {
					width: 80rpx;
					height: 80rpx;
					margin-right: 10rpx;
				}
			}
		}

		/* #endif */

		.page_content {
			background-color: #f5f5f5;
			/* #ifdef H5 */
			padding-bottom: 120rpx;
			/* #endif */
			padding-top: 20rpx;
			padding-bottom: calc(120rpx +constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
			padding-bottom: calc(120rpx +env(safe-area-inset-bottom)); ///兼容 IOS>11.2/

			.swiper {
				position: relative;
				width: 100%;
				height: 246rpx;
				margin: 0 auto;
				border-radius: 10rpx;
				overflow: hidden;
				margin-bottom: 25rpx;
				/* #ifdef MP */
				margin-top: 20rpx;

				/* #endif */
				swiper,
				swiper-item,
				.slide-image,
				image {
					width: 100%;
					height: 246rpx;
					border-radius: 10rpx;
				}
			}

			.nav {
				padding-bottom: 20rpx;
				background: #fff;
				opacity: 1;
				border-radius: 14rpx;
				width: 100%;
				margin-bottom: 20rpx;

				.item {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					width: 20%;
					margin-top: 20rpx;

					image {
						width: 82rpx;
						height: 82rpx;
					}
				}
			}

			.nav-bd {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.item {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					.txt {
						font-size: 32rpx;
						color: #282828;
					}

					.label {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 124rpx;
						height: 32rpx;
						margin-top: 5rpx;
						font-size: 24rpx;
						color: #999;
					}

					&.active {
						color: $theme-color;

						.txt {
							@include main-color(theme);
						}

						.label {
							// background: linear-gradient(90deg, $bg-star 0%, $bg-end 100%);
							@include linear-gradient(theme);
							border-radius: 16rpx;
							color: #fff;
						}
					}
				}
			}

			.index-product-wrapper {
				margin-bottom: 110rpx;

				&.on {
					min-height: 1500rpx;
				}

				.list-box {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;

					.item {
						width: 335rpx;
						margin-bottom: 20rpx;
						background-color: #fff;
						border-radius: 10rpx;
						overflow: hidden;

						image {
							width: 100%;
							height: 330rpx;
						}

						.text-info {
							padding: 10rpx 20rpx 15rpx;

							.title {
								color: #222222;
							}

							.old-price {
								margin-top: 8rpx;
								font-size: 26rpx;
								color: #AAAAAA;
								text-decoration: line-through;

								text {
									margin-right: 2px;
									font-size: 20rpx;
								}
							}

							.price {
								display: flex;
								align-items: flex-end;
								@include price-color(theme);
								font-size: 34rpx;
								font-weight: 800;
								margin-top: 0;

								text {
									padding-bottom: 4rpx;
									font-size: 24rpx;
									font-weight: 800;
								}

								.txt {
									display: flex;
									align-items: center;
									justify-content: center;
									width: 28rpx;
									height: 28rpx;
									margin-left: 15rpx;
									margin-bottom: 10rpx;
									border: 1px solid $theme-color;
									border-radius: 4rpx;
									font-size: 22rpx;
									font-weight: normal;
								}
							}
						}
					}

					&.on {
						display: flex;
					}
				}
			}
		}
	}

	// .pictrue {
	// 	position: relative;
	// 	width: 82rpx;
	// 	height: 82rpx;
	// 	border-radius: 50%;
	// 	overflow: hidden;
	// 	margin-bottom: 8rpx;
	// 	margin: 0 auto;
	// }

	.fixed {
		z-index: 100;
		position: fixed;
		left: 0;
		top: 0;
		background: linear-gradient(90deg, red 50%, #ff5400 100%);

	}

	.mores-txt {
		width: 100%;
		align-items: center;
		justify-content: center;
		height: 70rpx;
		color: #999;
		font-size: 24rpx;

		.iconfont {
			margin-top: 2rpx;
			font-size: 20rpx;
		}
	}

	.menu-txt {
		font-size: 24rpx;
		color: #454545;
	}

	.mp-bg {
		position: absolute;
		left: 0;
		/* #ifdef H5 */
		top: 98rpx;
		/* #endif */
		width: 100%;
		height: 246rpx;
		@include index-gradient(theme);
	}

	.mer-list {
		display: flex;
		align-items: center;
		color: #fff;

		.mer-name {
			max-width: 300rpx;
			padding-left: 5rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			font-size: 28rpx;
		}

		.mer-xiala {
			@include main-color(theme);
			font-size: 26rpx;
			margin-left: 10rpx;
		}
	}

	.mer-home {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 0 18rpx;
		height: 48rpx;
		border-radius: 24rpx;
		background: theme;
		font-weight: 500;
		font-size: 22rpx;
		color: #FFFFFF;
		margin-left: 30rpx;
		@include linear-gradient(theme);
	}

	.mer-logo {
		width: 100%;
		position: relative;
		padding: 0 15rpx;

		image {
			width: 100%;
			max-height: 180px;
			border-radius: 10rpx;
		}
	}

	.dialog-mer-nav {
		position: fixed;
		left: 40rpx;
		background: #FFFFFF;
		box-shadow: 0px 0px 16rpx rgba(0, 0, 0, 0.08);
		z-index: 310;
		border-radius: 15rpx;

		&::before {
			content: '';
			width: 0;
			height: 0;
			position: absolute;
			left: 0;
			right: 0;
			margin: auto;
			top: -9px;
			border-bottom: 10px solid #F5F5F5;
			border-left: 10px solid transparent;
			border-right: 10px solid transparent;
		}

		.dialog-mer-nav-item {
			width: 100%;
			height: 90rpx;
			line-height: 90rpx;
			padding: 0 20rpx 0;
			box-sizing: border-box;
			border-bottom: #eee;
			font-size: 28rpx;
			color: #333;
			position: relative;
			display: flex;

			.mer-icon {
				width: 90rpx;
				height: 90rpx;

				image {
					width: 80rpx;
					height: 80rpx;
				}
			}

			.mer-name {}
		}
	}

	.dialog-mer-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: transparent;
		z-index: 300;
	}
</style>
<template>
	<view class="goods-category-content">
		<!-- 顶部导航栏 -->
		<view class="goods-category-top-nav-box">
			<view :style=" {height:statusBarHeight+'px'}"></view>
			<view class="goods-category-top-nav-title"
				:style="{height:titleBarHeight+'px','line-height':titleBarHeight+'px'}">
				<uni-icons color="#222222" type="back" size="30" @click="goBack"></uni-icons>
				<view class="goods-category-top-nav-title-text">商品分类</view>
			</view>
		</view>
		<!-- 占位 -->
		<view :style="{height:headerHeight+'px'}"></view>
		<view class="goods-category-search-box">
			<view class="goods-category-search-main-box" @click="goSearchPage">
				<image class="search-icon" src="/pages/home/<USER>/search-icon.png" mode="aspectFit"></image>
				<view class="search-placeholder-text">搜索商品或品牌名称</view>
				<view class="search-btn-box">搜索</view>
			</view>
		</view>
		<view class="goods-category-main-box">
			<view class="goods-category-main-aside-box">
				<scroll-view scroll-y="true" class="goods-category-main-aside-scroll-box"
					style="height: 100%; overflow: hidden;">
					<view class="aside-scroll-item-box" v-for="item in categoryList" :key="item.id">
						<view class="aside-scroll-item-parent"
							:style="{backgroundColor: categoryParentId == item.id ? '#ffffff' : '#f5f5f5'}"
							@click="openFn(item.id,item.childList[0].id)">{{item.name}}</view>
						<view class="aside-scroll-item-child-box" v-if="categoryParentId == item.id">
							<view class="child-item-box" v-for="(ite,ind) in item.childList" :key="ite.id"
								@click="chooseClass(ind,ite.id)">
								<view style="position: relative;z-index: 5;">{{ite.name}}</view>
								<view v-show="categoryChildIndex == ind" class="child-item-line"></view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="goods-category-main-conter-box">
				<scroll-view scroll-y="true" class="goods-category-main-conter-scroll-box"
					style="height: 100%; overflow: hidden;" @scrolltolower="scrollToLowerFn">
					<view class="conter-header-fixed-box">
						<view class="conter-banner-box">
							<swiper class="conter-banner-swiper-box" circular :autoplay="true">
								<swiper-item class="conter-banner-swiper-item" v-for="item in categoryCarouselList" :key="item.id">
									<image class="conter-banner-swiper-item-img" :src="item.imageUrl" mode="aspectFit"></image>
								</swiper-item>
							</swiper>
						</view>
						<!-- 三级分类区域 -->
						<view class="third-category-container" v-if="thirdCategoryList.length > 0">
							<view class="third-category-content" :class="{'expanded': isThirdCategoryExpanded}">
								<scroll-view scroll-x="true" class="third-category-scroll" show-scrollbar="false">
									<view class="third-category-list">
										<view
											class="third-category-item"
											v-for="item in thirdCategoryList"
											:key="item.id"
											:class="{'active': selectedThirdCategoryId === item.id}"
											@click="selectThirdCategory(item.id)"
										>
											{{item.name}}
										</view>
									</view>
								</scroll-view>
							</view>
							<view class="third-category-toggle" @click="toggleThirdCategoryExpanded" v-if="thirdCategoryList.length > 4">
								<image
									class="toggle-icon"
									:src="isThirdCategoryExpanded ? '/static/images/up.png' : '/static/images/down.png'"
									mode="aspectFit"
								></image>
							</view>
						</view>
						<view class="conter-fillter-box">
							<view class="conter-fillter-item" :style="{color: fillterType === '1' ? '#222222' : '#999999'}"
								@click="switchConditionsFn('1')">销量</view>
							<view class="conter-fillter-item" :style="{color: fillterType === '2' ? '#222222' : '#999999'}"
								@click="switchConditionsFn('2')">
								<view class="">价格</view>
								<image v-if="price === 1" class="fillter-item-icon" src="/pages/home/<USER>/search-sort-top.png"
									mode="aspectFit"></image>
								<image v-else-if="price === 2" class="fillter-item-icon" src="/pages/home/<USER>/search-sort-bottom.png"
									mode="aspectFit">
								</image>
							</view>
						</view>
						<view class="conter-line"></view>
					</view>
					<view class="conter-header-fixed-placeholder"></view>
					<view class="conter-goods-list-box">
						<view class="conter-goods-item-box" v-for="item in productList" :key="item.id" @click="goToDeatils(item)">
							<!-- <view class="goods-item-img"></view> -->
							<image class="goods-item-img" :src="item.image" mode="aspectFit"></image>
							<view class="goods-item-info">
								<view class="goods-item-name">{{item.name}}</view>
								<view class="goods-item-price-box">
									<view class="goods-item-price-text">￥{{item.price}}</view>
									<image v-if="item.type == 3" class="goods-item-type"
										src="/static/imgs/home/<USER>" mode="aspectFit"></image>
									<image v-else-if="item.type == 2" class="goods-item-type"
										src="/static/imgs/home/<USER>" mode="aspectFit"></image>
									<image v-else-if="item.type == 1" class="goods-item-type"
										src="/static/imgs/home/<USER>" mode="aspectFit"></image>
								</view>
								<view class="goods-item-bottom">
									<view class="goods-item-bottom-price">¥{{item.otPrice}}</view>
									<uni-badge class="uni-badge-left-margin" :text="item.cartNum" absolute="rightTop"
										:offset="item.cartNum > 0 ? [6, 0] : [-10, 0]" size="small">
										<image class="goods-item-bottom-add" src="/static/imgs/home/<USER>" mode="aspectFit"
											@click="addGoodsFn(item)"></image>
									</uni-badge>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<!-- 选择门店弹层 -->
		<uni-popup ref="addPopup" type="bottom" border-radius="15px 15px 0 0" :is-mask-click="false"
			background-color="#fff">
			<view class="add-goods-popup-content">
				<view class="add-goods-popup-header-box">
					<view class="add-goods-popup-header-title">规格选择</view>
					<image class="add-goods-popup-header-close" src="/static/imgs/home/<USER>"
						mode="aspectFit" @click="closeAddPopup"></image>
				</view>
				<!-- 添加商品弹层主结构 -->
				<view class="add-goods-popup-main-box">
					<!-- 商品信息 -->
					<view class="goods-info-box">
						<image class="goods-info-img" :src="productDetail.productInfo.image" mode=""></image>
						<view class="goods-info-intro">
							<view class="goods-info-price-box">
								<view class="goods-info-price-text">￥{{productDetail.productInfo.price}}</view>
								<view class="goods-info-type"></view>
							</view>
							<view class="goods-info-old-price">￥{{productDetail.productInfo.otPrice}}</view>
							<view class="goods-info-name">{{productDetail.productInfo.name}}</view>
						</view>
					</view>
					<!-- 商品规格信息 -->
					<view class="goods-info-specification">
						<view class="specification-item-box" v-for="item in productDetail.productAttr" :key="item.id">
							<view class="specification-item-title">{{item.attrName}}</view>
							<view class="specification-item-child-list-box">
								<view class="specification-item-child"
									:style="[{backgroundColor: specItem == ite ? '#bdfd5b' : '#f5f5f5'},{color: specItem == ite ? '#222222' : '#666666'}]"
									v-for="(ite,i) in item.attrValues.split(',')" :key="i" @click="() => chooseSpecFn(ite)">{{ite}}</view>
							</view>
						</view>
					</view>
				</view>
				<view class="goods-info-footer-placeholder"></view>
				<view class="goods-info-footer-box">
					<view class="add-cart-box" @click="addCartFn">加入购物车</view>
					<view class="buy-now--box" @click="buyNowFn">立即购买</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import {
		getCategoryList,
		postCartAdd,
		getProductDetail,
		getLens
	} from '@/api/product.js';
	import {
		getCategoryThird
	} from '@/api/api.js';
	import {
		getCategoryCarouselApi
	} from '@/api/home.js';
	import {
		getMerProListApi
	} from '@/api/merchant.js'
	import {
		preOrderApi
	} from '@/api/order.js'
	export default {
		data() {
			return {
				menuButtonInfo: null, // 胶囊按钮信息
				statusBarHeight: 0, // 状态栏高度
				titleBarHeight: 0, // 标题栏高度
				headerHeight: 0, // 高度占位
				categoryList: [], // 分类树
				categoryParentId: null, // 当前选中的父级id
				categoryChildIndex: 0, // 当前选中的子级索引
				categoryChildId: null, // 当前选中的子级id
				// 三级分类相关
				thirdCategoryList: [], // 三级分类列表
				selectedThirdCategoryId: null, // 当前选中的三级分类id
				isThirdCategoryExpanded: false, // 三级分类是否展开
				// 请求参数
				queryObj: {
					catId: '', // 三级分类id
					cid: '', // 分类id
					keyword: '', // 搜索关键字
					limit: 10, // 每页数量
					maxPrice: '', // 最高价
					merId: 1, // 商户id
					minPrice: '', // 最低价
					page: 1, // 页码
					priceOrder: '', // 价格排序 desc 降序(下) asc升序(上)
					salesOrder: '', // 销量排序 desc 降序(下) asc升序(上)
				},
				productList: [], // 商品列表
				productListTotal: 0, // 商品列表总数
				isProductLoading: false, // 记录请求状态, true 加载中, false 加载完毕
				fillterType: '1', // 筛选条件    1 销量  2 价格
				price: 0, // 价格排序 2 降序(下) 1升序(上)
				productDetail: {}, // 商品详情
				specItem: '', // 点击选择的规格
				specVal: null, // 点击选择的商品规格详情
				categoryCarouselList: [], // 轮播图列表
			};
		},
		onLoad() {
			// 获取状态栏高度
			// const info = uni.getSystemInfoSync() // 获取设备信息
			const info = uni.getWindowInfo() // 获取设备信息
			// console.log('info', info);
			this.statusBarHeight = info.statusBarHeight
			// 获取胶囊按钮信息(width, height, top等)
			const menuButton = uni.getMenuButtonBoundingClientRect()
			// console.log('menuButton', menuButton);
			this.menuButtonInfo = menuButton
			// 胶囊按钮相对于导航栏的上边距
			const topDistance = this.menuButtonInfo.top - this.statusBarHeight
			// 计算导航栏高度
			this.titleBarHeight = this.menuButtonInfo.height + topDistance * 2
			this.headerHeight = this.titleBarHeight + this.statusBarHeight
			this.getCategoryListFn()
			this.getCategoryCarousel()
		},
		onShow() {},
		methods: {
			// 点击返回上一页
			goBack() {
				uni.switchTab({
					url: "/pages/index/index"
				})
			},
			// 点击前往搜索页面
			goSearchPage() {
				uni.navigateTo({
					url: `/pages/home/<USER>/search-product?merchantId=${JSON.stringify(1)}`
				})
			},
			// 获取商户轮播图
			async getCategoryCarousel() {
				const res = await getCategoryCarouselApi({
					merchantId: '1'
				})
				if (res.code === 200) {
					console.log('分类轮播图', res);
					this.categoryCarouselList = res.data
				}
			},
			// 获取分类缓存树
			async getCategoryListFn() {
				const res = await getCategoryList()
				console.log('获取分类缓存树', res);
				this.categoryList = res.data
				console.log('categoryList', this.categoryList);
				this.categoryParentId = this.categoryList[0].id
				this.categoryChildIndex = 0
				this.categoryChildId = this.categoryList[0].childList[0].id
				this.queryObj.cid = this.categoryChildId
				// 获取初始三级分类
				await this.getThirdCategoryList(this.categoryChildId)
				this.getProductList()
			},
			// 点击打开下一级
			async openFn(id, childId) {
				if (this.categoryParentId === id) {
					return
				}
				console.log('点击打开下一级', id);
				console.log('点击打开下一级id', childId);
				this.categoryParentId = id
				this.categoryChildIndex = 0
				this.categoryChildId = childId
				this.queryObj.cid = this.categoryChildId
				this.productList = []
				// 获取三级分类
				await this.getThirdCategoryList(childId)
				this.getProductList()
			},
			// 点击切换分类商品
			chooseClass(i, id) {
				this.categoryChildIndex = i
				this.categoryChildId = id
				this.queryObj.cid = this.categoryChildId
				this.productList = []
				// 获取三级分类
				this.getThirdCategoryList(id)
				this.getProductList()
			},
			// 获取三级分类列表
			async getThirdCategoryList(secondCategoryId) {
				try {
					const res = await getCategoryThird(secondCategoryId)
					if (res.code === 200) {
						this.thirdCategoryList = res.data || []
						// 如果有三级分类，默认选中第一个
						if (this.thirdCategoryList.length > 0) {
							this.selectedThirdCategoryId = this.thirdCategoryList[0].id
							this.queryObj.catId = this.selectedThirdCategoryId
						} else {
							this.selectedThirdCategoryId = null
							this.queryObj.catId = ''
						}
					}
				} catch (error) {
					console.error('获取三级分类失败:', error)
					this.thirdCategoryList = []
					this.selectedThirdCategoryId = null
					this.queryObj.catId = ''
				}
			},
			// 选择三级分类
			selectThirdCategory(categoryId) {
				this.selectedThirdCategoryId = categoryId
				this.queryObj.catId = categoryId
				this.productList = []
				this.getProductList()
			},
			// 切换三级分类展开/收起状态
			toggleThirdCategoryExpanded() {
				this.isThirdCategoryExpanded = !this.isThirdCategoryExpanded
			},
			// 获取商品列表
			async getProductList() {
				this.isProductLoading = true
				const res = await getMerProListApi(this.queryObj)
				if (res.code === 200) {
					console.log('获取商品列表', res);
					this.productList = [...this.productList, ...res.data.list]
					this.productListTotal = res.data.total
					this.isProductLoading = false
					console.log('this.productList', this.productList);
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 触底加载下一页
			scrollToLowerFn() {
				if (this.isProductLoading) return
				this.queryObj.page++
				this.getProductList()
				if (this.queryObj.page * this.queryObj.limit >= this.productListTotal) return uni.showToast({
					icon: 'none',
					duration: 2000,
					title: '数据加载完成'
				})
			},
			// 点击切换销量与价格
			switchConditionsFn(type) {
				if (type == '1') {
					console.log('销量');
					this.fillterType = type
					this.queryObj.page = 1
					this.price = 0
					this.queryObj.priceOrder = ''
					this.queryObj.salesOrder = 'asc'
					this.productList = []
					this.getProductList()
				} else if (type === '2') {
					this.fillterType = type
					this.queryObj.page = 1
					this.sales = 0
					if (this.price === 0 || this.price === 2) {
						this.price = 1
						this.queryObj.priceOrder = 'asc'
						this.queryObj.salesOrder = ''
						this.productList = []
						this.getProductList()
					} else if (this.price === 1) {
						this.price = 2
						this.queryObj.priceOrder = 'desc'
						this.queryObj.salesOrder = ''
						this.productList = []
						this.getProductList()
					}
				}
			},
			// 点击添加/购买商品
			async addGoodsFn(item) {
				console.log('点击添加/购买商品', item);
				if (item.skuType == 2) {
					this.specItem = ''
					this.specVal = null
					this.$refs.addPopup.open()
					this.getProductDetailFn(item.id, 'normal')
					this.getLensFn()
				} else {
					console.log('加入购物车');
					const res = await postCartAdd({
						cartNum: 1,
						productAttrUnique: item.skuId,
						productId: item.id,
						storeId: uni.getStorageSync('storeId') || 0,
						customData: ''
					})
					if (res.code === 200) {
						uni.showToast({
							icon: 'none',
							duration: 2000,
							title: '添加购物车成功'
						})
						this.productList = []
						this.getProductList()
					}
				}
			},
			// 点击关闭商品弹层
			closeAddPopup() {
				this.$refs.addPopup.close()
			},
			// 获取商品详情
			async getProductDetailFn(id, type) {
				const res = await getProductDetail(id, type, uni.getStorageSync('storeId') || 0)
				console.log('商品详情', res);
				this.productDetail = res.data
			},
			// 点击选择规格
			chooseSpecFn(item) {
				console.log('item', item);
				this.specItem = item
				// this.productDetail.productValue.forEach(item, i)
				Object.keys(this.productDetail.productValue).forEach(key => {
					if (key == item) {
						console.log('000', this.productDetail.productValue[key]);
						this.specVal = this.productDetail.productValue[key]
					}
				})
			},
			// 获取眼睛规格列表
			async getLensFn() {
				const res = await getLens({
					merId: this.queryObj.merId,
					s: 0,
					c: 0
				})
				console.log('getLensFn', res);
			},
			// 点击加入购物车
			async addCartFn() {
				if (this.specVal == null) {
					return uni.showToast({
						icon: 'none',
						duration: 2000,
						title: '请选择规格'
					})
				}
				const res = await postCartAdd({
					cartNum: 1,
					productAttrUnique: this.specVal.id,
					productId: this.productDetail.productInfo.id,

					storeId: uni.getStorageSync('storeId') || 0,
					customData: ''
				})
				if (res.code === 200) {
					uni.showToast({
						icon: 'none',
						duration: 2000,
						title: '添加购物车成功'
					})
					this.productList = []
					this.getProductList()
					this.$refs.addPopup.close()
				}
			},
			// 点击立即购买
			async buyNowFn() {
				if (this.specVal == null) {
					return uni.showToast({
						icon: 'none',
						duration: 2000,
						title: '请选择规格'
					})
				}
				const res = await preOrderApi({
					orderDetails: [{
						attrValueId: this.specVal.id,
						productId: this.productDetail.productInfo.id,
						productNum: 1,
						customData: ''
					}],
					preOrderType: 'buyNow',
					storesId: uni.getStorageSync('storeId') || 1,
				})
				if (res.code === 200) {
					uni.navigateTo({
						url: '/pages/goods/order_confirm/index?orderNo=' + res.data.orderNo
					});
					this.$refs.addPopup.close()
					// this.productList = []
				}
			},
			// 点击前往商品详情
			goToDeatils(item) {
				uni.setStorageSync('storeId', 1)
				uni.navigateTo({
					url: `/pages/goods/goods_details/index?id=${item.id}&merchantid=1`
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.goods-category-content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		width: 100%;

		.goods-category-top-nav-box {
			position: fixed;
			top: 0rpx;
			z-index: 1000;
			width: 100%;
			background: #f5f5f5;

			.goods-category-top-nav-title {
				display: flex;
				align-items: center;

				.goods-category-top-nav-title-text {
					flex: 1;
					text-align: center;
					font-size: 34rpx;
					color: #222222;
				}
			}
		}

		.goods-category-search-box {
			margin-top: 20rpx;
			margin-bottom: 20rpx;
			display: flex;
			justify-content: center;
			width: 750rpx;
			height: 68rpx;

			.goods-category-search-main-box {
				display: flex;
				align-items: center;
				padding-left: 25rpx;
				padding-right: 10rpx;
				width: 710rpx;
				height: 68rpx;
				background: #ffffff;
				border: 4rpx solid #222222;
				border-radius: 8rpx;

				.search-icon {
					margin-right: 10rpx;
					width: 30rpx;
					height: 30rpx;
				}

				.search-placeholder-text {
					flex: 1;
					font-size: 26rpx;
					color: #b2b2b2;
				}

				.search-btn-box {
					width: 96rpx;
					height: 48rpx;
					background: #bdfd5b;
					border-radius: 12rpx;
					text-align: center;
					line-height: 48rpx;
					font-size: 24rpx;
					color: #222222;
				}
			}
		}

		.goods-category-main-box {
			display: flex;
			width: 750rpx;
			flex: 1;
			overflow: hidden;

			// background-color: #222222;
			.goods-category-main-aside-box {
				margin-right: 10rpx;
				width: 172rpx;
				height: 100%;
				background: #ffffff;
				overflow-y: auto;
				overflow-x: hidden;
				overflow: hidden;

				.goods-category-main-aside-scroll-box {
					width: 172rpx;
					// height: 80%;
					background-color: #ffffff;

					.aside-scroll-item-box {
						width: 172rpx;

						.aside-scroll-item-parent {
							width: 172rpx;
							height: 96rpx;
							// background: #f5f5f5;
							font-size: 28rpx;
							text-align: center;
							line-height: 96rpx;
							color: #666666;
						}

						.aside-scroll-item-child-box {
							width: 172rpx;

							.child-item-box {
								position: relative;
								width: 172rpx;
								height: 96rpx;
								background: #ffffff;
								font-size: 28rpx;
								text-align: center;
								line-height: 96rpx;
								color: #666666;
								font-weight: 700;

								.child-item-line {
									position: absolute;
									left: 50%;
									bottom: 33rpx;
									transform: translateX(-50%);
									width: 64rpx;
									height: 10rpx;
									background: #bdfd5b;
									border-radius: 4rpx 0rpx 0rpx 4rpx;
								}
							}
						}
					}
				}
			}

			.goods-category-main-conter-box {
				// width: 172rpx;
				// height: 100%;
				// background: #ffffff;
				// overflow-y: auto;
				// overflow-x: hidden;
				// overflow: hidden;
				flex: 1;
				height: 100%;
				padding: 0 15rpx;
				background-color: #ffffff;

				.goods-category-main-conter-scroll-box {
					width: 100%;
					background-color: #ffffff;

					.conter-header-fixed-box {
						position: fixed;
						width: 538rpx;
						background-color: #ffffff;
						z-index: 2;

						.conter-banner-box {
							margin-bottom: 20rpx;
							width: 538rpx;
							height: 156rpx;
							// background: #d8d8d8;
							border-radius: 16rpx;

							.conter-banner-swiper-box {
								width: 538rpx;
								height: 156rpx;
								border-radius: 16rpx;

								.conter-banner-swiper-item {
									// display: block;
									width: 538rpx;
									height: 156rpx;
									border-radius: 16rpx;

									.conter-banner-swiper-item-img {
										width: 538rpx;
										height: 156rpx;
										border-radius: 16rpx;
									}
								}
							}
						}

						.conter-fillter-box {
							display: flex;
							justify-content: flex-end;
							align-items: center;
							margin-top: 20rpx;
							margin-bottom: 20rpx;
							width: 100%;

							.conter-fillter-item {
								display: flex;
								align-items: center;
								margin-right: 30rpx;
								font-size: 24rpx;
								// color: #999999;

								&:last-child {
									margin-right: 0;
								}

								.fillter-item-icon {
									width: 20rpx;
									height: 18rpx;
								}
							}
						}

						.conter-line {
							margin-bottom: 30rpx;
							width: 100%;
							height: 2rpx;
							background: #f5f5f5;
						}
					}

					// 三级分类样式
					.third-category-container {
						display: flex;
						align-items: center;
						margin-bottom: 20rpx;
						width: 100%;

						.third-category-content {
							flex: 1;
							max-height: 80rpx;
							overflow: hidden;
							transition: max-height 0.3s ease;

							&.expanded {
								max-height: 240rpx; // 支持3行显示
							}

							.third-category-scroll {
								width: 100%;
								white-space: nowrap;

								.third-category-list {
									display: flex;
									flex-wrap: wrap;

									.third-category-item {
										display: inline-block;
										margin-right: 20rpx;
										margin-bottom: 20rpx;
										padding: 12rpx 24rpx;
										background: #f5f5f5;
										color: #666666;
										font-size: 24rpx;
										border-radius: 40rpx;
										white-space: nowrap;
										transition: all 0.3s ease;

										&.active {
											background: #bdfd5b;
											color: #222222;
										}

										&:last-child {
											margin-right: 0;
										}
									}
								}
							}
						}

						.third-category-toggle {
							margin-left: 20rpx;
							width: 60rpx;
							height: 60rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							background: #f5f5f5;
							border-radius: 50%;

							.toggle-icon {
								width: 24rpx;
								height: 24rpx;
								transition: transform 0.3s ease;
							}
						}
					}

					.conter-header-fixed-placeholder {
						width: 100%;
						height: 260rpx;
					}

					.conter-goods-list-box {
						width: 100%;

						.conter-goods-item-box {
							display: flex;
							align-items: center;
							margin-bottom: 60rpx;
							width: 100%;

							.goods-item-img {
								margin-right: 20rpx;
								width: 160rpx;
								height: 160rpx;
								border-radius: 16rpx;
								// background-color: #bdfd5b;
							}

							.goods-item-info {
								width: 358rpx;

								.goods-item-name {
									margin-bottom: 8rpx;
									width: 358rpx;
									font-size: 28rpx;
									color: #333333;
									line-clamp: 2;
									overflow: hidden;
									text-overflow: ellipsis;
									text-overflow: -webkit-ellipsis-lastline;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;
								}

								.goods-item-price-box {
									display: flex;
									align-items: center;
									width: 100%;

									.goods-item-price-text {
										margin-right: 4rpx;
										font-size: 36rpx;
										font-weight: 700;
										color: #ff2222;
									}

									.goods-item-type {
										width: 62rpx;
										height: 26rpx;
									}
								}

								.goods-item-bottom {
									display: flex;
									justify-content: space-between;
									align-items: center;
									width: 100%;

									.goods-item-bottom-price {
										font-size: 28rpx;
										color: #b2b2b2;
										text-decoration: line-through;
									}

									.goods-item-bottom-add {
										width: 48rpx;
										height: 48rpx;
									}
								}
							}
						}
					}
				}
			}
		}

		.add-goods-popup-content {
			position: relative;
			padding-top: 30rpx;
			width: 750rpx;
			height: 75vh;

			.add-goods-popup-header-box {
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;
				padding-right: 26rpx;
				width: 100%;

				.add-goods-popup-header-title {
					flex: 1;
					text-align: center;
					font-size: 30rpx;
					color: #222222;
				}

				.add-goods-popup-header-close {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.add-goods-popup-main-box {
				padding-left: 30rpx;
				padding-right: 20rpx;
				width: 100%;

				.goods-info-box {
					margin-bottom: 38rpx;
					display: flex;
					width: 100%;

					.goods-info-img {
						margin-right: 30rpx;
						width: 200rpx;
						height: 200rpx;
						border-radius: 8rpx;
					}

					.goods-info-intro {
						flex: 1;

						.goods-info-price-box {
							display: flex;
							margin-bottom: 10rpx;
							width: 100%;

							.goods-info-price-text {
								font-size: 48rpx;
								font-weight: 700;
								color: #ff2222;
							}
						}

						.goods-info-old-price {
							margin-bottom: 10rpx;
							font-size: 28rpx;
							text-decoration: line-through;
							color: #b2b2b2;
						}

						.goods-info-name {
							width: 100%;
							font-size: 30rpx;
							color: #333333;
							line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
							text-overflow: -webkit-ellipsis-lastline;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							-webkit-box-orient: vertical;
						}
					}
				}

				.goods-info-specification {
					width: 100%;

					.specification-item-box {
						width: 100%;

						.specification-item-title {
							margin-bottom: 20rpx;
							font-size: 30rpx;
							color: #333333;
						}

						.specification-item-child-list-box {
							display: flex;
							flex-wrap: wrap;
							width: 100%;

							.specification-item-child {
								margin-right: 30rpx;
								padding: 0 30rpx;
								height: 56rpx;
								background: #f5f5f5;
								border-radius: 8rpx;
								line-height: 56rpx;
								font-size: 26rpx;
								color: #666666;

								&:last-child {
									margin-right: 0;
								}
							}
						}

					}
				}

			}

			.goods-info-footer-placeholder {
				width: 750rpx;
				height: 98rpx;
			}

			.goods-info-footer-box {
				position: fixed;
				left: 0;
				bottom: 0;
				display: flex;
				justify-content: space-between;
				padding: 0 30rpx;
				width: 100%;
				height: 98rpx;
				background-color: #ffffff;

				.add-cart-box {
					width: 334rpx;
					height: 88rpx;
					background: #ff8125;
					border-radius: 16rpx;
					text-align: center;
					line-height: 88rpx;
					font-size: 28rpx;
					color: #ffffff;
				}

				.buy-now--box {
					width: 334rpx;
					height: 88rpx;
					background: #bdfd5b;
					border-radius: 16rpx;
					text-align: center;
					line-height: 88rpx;
					font-size: 28rpx;
					color: #222222;
				}
			}
		}
	}
</style>
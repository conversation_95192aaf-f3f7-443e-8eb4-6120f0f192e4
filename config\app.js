// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
//移动端商城API

// let domain = 'https://pf.xinhuasp.com'

// let domain = 'http://192.168.1.2:8081'
// let domain = 'https://pf.letengshengtai.com'
// let domain = 'http://hhlm.iepose.cn'
let domain = 'http://qg52961sd27.vicp.fun'

// let domain = 'http://47.94.122.58:10006'
// let domain = 'http://ooseek.iepose.cn'
// let domain = 'http://192.168.31.55:8081'
// let domain = 'https://pf.xinhuasp.com'


module.exports = {
	// 请求域名 格式： https://您的域名
	// #ifdef MP || APP-PLUS
	// HTTP_REQUEST_URL:'',
	HTTP_REQUEST_URL: domain,
	// H5商城地址
	HTTP_H5_URL: 'http://你的H5域名',
	// #endif
	// #ifdef H5
	HTTP_REQUEST_URL: domain,
	// #endif
	HEADER: {
		'content-type': 'application/json'
	},
	HEADERPARAMS: {
		'content-type': 'application/x-www-form-urlencoded'
	},
	// 回话密钥名称 请勿修改此配置
	TOKENNAME: 'Authori-zation',
	// 缓存时间 0 永久
	EXPIRE: 0,
	//分页最多显示条数
	LIMIT: 10
};
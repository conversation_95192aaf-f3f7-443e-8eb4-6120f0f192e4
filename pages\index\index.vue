<template>
	<view class="home-content">
		<!-- 头部区域 -->
		<view class="home-header-box"
			:style="{height: fitmentInfoObj.adsImg ? `${fitmentInfoObj.adsHeight}rpx` : `592rpx`}">
			<image v-if="fitmentInfoObj.adsImg" class="home-header-bgc-box"
				:style="{height: `${fitmentInfoObj.adsHeight}rpx`}" :src="fitmentInfoObj.adsImg" mode="aspectFit"
				@click="goToLink(fitmentInfoObj.adsUrl)"></image>
			<image v-else class="home-header-bgc-box" src="/pages/coupon/static/imgs/home/<USER>"
				mode="aspectFit"></image>
			<view :style=" {height:statusBarHeight+'px'}"></view>
			<view class="home-header-store-name-box">
				<!-- <view class="home-header-store-icon"></view> -->
				<image class="home-header-store-icon" src="/static/imgs/home/<USER>" mode="aspectFit"></image>
				<view class="home-header-store-name-main" @click="chooseStore">
					<view class="home-header-store-name-text">乐腾生态平台</view>
					<image class="home-header-store-name-down-icon" src="/static/imgs/home/<USER>" mode="aspectFit">
					</image>
				</view>
			</view>
			<view class="home-header-search-box">
				<view class="home-header-search-main" @click="goSearch">
					<image class="home-header-search-icon" src="/static/imgs/home/<USER>" mode="aspectFit">
					</image>
					<view class="home-header-search-input">高档老花镜</view>
					<view class="home-header-search-btn">搜索</view>
				</view>
			</view>
		</view>
		<view class="home-main-box">
			<view class="home-main">
				<!-- 金刚区 -->
				<view class="home-main-nav-box">
					<view class="nav-header-box">
						<view class="nav-header-item-box" v-for="item in fitmentInfoObj.oneFunctions" :key="item"
							@click="goToLink(item.linkUrl)">
							<!-- <view class="nav-header-item-icon"></view> -->
							<image class="nav-header-item-icon" :src="item.iconUrl" mode="aspectFit"></image>
							<view class="nav-header-item-title">{{item.name}}</view>
							<view class="nav-header-item-msg">{{item.illustrate}}</view>
						</view>
					</view>
					<view class="nav-footer-box">
						<view class="nav-footer-item-box" v-for="item in fitmentInfoObj.twoFunctions" :key="item"
							@click="goToLink(item.linkUrl)">
							<!-- <view class="nav-footer-item-icon"></view> -->
							<image class="nav-footer-item-icon" :src="item.iconUrl" mode="aspectFit"></image>
							<view class="nav-footer-item-title">{{item.name}}</view>
							<view class="nav-footer-item-msg">{{item.illustrate}}</view>
						</view>
					</view>
				</view>
				<!-- 间隔占位 -->
				<view style="width: 100%"
					:style="[{height: `${fitmentInfoObj.spacingHeight}rpx`},{backgroundColor: fitmentInfoObj.spacingColor === 1 ? '#ffffff' : '#f5f5f5'}]">
				</view>
				<!-- 新人专属福利 -->
				<view class="home-main-newcomer-benefits-box">
					<!-- @/pages/coupon/static/imgs/home/<USER>
					<image class="newcomer-benefits-bgc" src="https://pa.letengshengtai.com/ooseekimage/public/product/2025/07/23/3660609feffe41269c30ade14cd686ed3sfo8a2z1u.png" mode="aspectFit"
						@click="acceptCouponFn">
					</image>
					<view class="newcomer-benefits-more-box" @click="goToCoupon(fitmentInfoObj.couponInfoList)">
						<view class="newcomer-benefits-more-text">更多</view>
						<!--  @/pages/coupon/static/imgs/home/<USER>
						<image class="newcomer-benefits-more-icon" src="/pages/coupon/static/imgs/home/<USER>" mode="aspectFit"></image>
					</view>
					<image src="" mode=""></image>
					<view class="newcomer-benefits-price-box" v-if="fitmentInfoObj.couponInfoList[0].couponType == '1'"
						@click="acceptCouponFn">
						<view class="newcomer-benefits-price-symbol">￥</view>
						<view class="newcomer-benefits-price-num">{{fitmentInfoObj.couponInfoList[0].money}}</view>
					</view>
					<view class="newcomer-benefits-price-box" v-else-if="fitmentInfoObj.couponInfoList[0].couponType == '2'"
						@click="acceptCouponFn">
						<view class="newcomer-benefits-price-num">{{fitmentInfoObj.couponInfoList[0].discount * 10}}</view>
						<view class="newcomer-benefits-price-symbol">折</view>
					</view>
					<view class="newcomer-benefits-intro-box" @click="acceptCouponFn">
						<view class="newcomer-benefits-intro-title">{{fitmentInfoObj.couponInfoList[0].name}}</view>
						<view class="newcomer-benefits-intro-msg">
							{{ fitmentInfoObj.couponInfoList[0].minPrice !== 0 ? `满${fitmentInfoObj.couponInfoList[0].minPrice}元可用` : '无门槛' }}
						</view>
					</view>
				</view>
				<!-- 间隔占位 -->
				<view style="width: 100%"
					:style="[{height: `${fitmentInfoObj.spacingHeight}rpx`},{backgroundColor: fitmentInfoObj.spacingColor === 1 ? '#ffffff' : '#f5f5f5'}]">
				</view>
				<!-- 商户推荐 -->
				<view class="home-main-merchant-recommend-box">
					<view class="merchant-recommend-header-box">
						<view class="merchant-recommend-header-line"></view>
						<view class="merchant-recommend-header-title">商户推荐</view>
						<view class="merchant-recommend-header-msg">好物任您选</view>
					</view>
					<view class="merchant-recommend-list-box">
						<view class="merchant-recommend-list-main">
							<view class="merchant-recommend-item-box" v-for="item in fitmentInfoObj.merchants" :key="item.id"
								@click="goToMerchantHome(item.id)">
								<!-- <view class="merchant-recommend-item-img"></view> -->
								<image class="merchant-recommend-item-img" :src="item.avatar" mode="aspectFit"></image>
								<view class="merchant-recommend-item-main">
									<view class="merchant-recommend-item-name">{{item.name}}</view>
									<view class="merchant-recommend-item-address">{{item.addressDetail}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 间隔占位 -->
				<view style="width: 100%"
					:style="[{height: `${fitmentInfoObj.spacingHeight}rpx`},{backgroundColor: fitmentInfoObj.spacingColor === 1 ? '#ffffff' : '#f5f5f5'}]">
				</view>
				<!-- 热门推荐(瀑布流) -->
				<view class="home-main-hot-recommend-box">
					<image class="hot-recommend-header-box" src="/static/imgs/home/<USER>"
						mode="aspectFit"></image>
					<!-- 
						 value: 渲染的列表
						 column: 列数
						 maxColumn: 最大列数
						 columnSpace: 列之间的间距(单位是百分比)
						 imageKey: 列表中的图片字段的键名
						 hideImageKey: 隐藏图片字段的键名
						 seat: 自定义文字的位置,1-图片上方，2-图片下方
						 listStyle: 单个展示项的样式
						 @loaded: 图片加载完成事件
						 @wapperClick: 单项点击事件
						 @imageClick: 图片点击事件
						 -->
					<custom-waterfalls-flow ref="waterfallsFlowRef" :value="fitmentInfoObj.products" :column="column"
						:columnSpace="2.6" hideImageKey="image" :seat="2" @wapperClick="wapperClick" @imageClick="imageClick"
						@loaded="loaded">
						<view class="hot-recommend-item-box" v-for="(item,index) in fitmentInfoObj.products" :key="item.id"
							slot="slot{{index}}">
							<view class="hot-recommend-item-name">{{item.name}}</view>
							<view class="hot-recommend-item-intro">{{item.intro}}</view>
							<view class="hot-recommend-item-price-box">
								<view class="item-price">¥{{item.price}}</view>
								<view class="item-old-price">¥{{item.otPrice}}</view>
								<image v-if="item.type == '2'" class="item-icon" src="/static/imgs/home/<USER>"
									mode="aspectFit">
								</image>
								<image v-else-if="item.type == '1'" class="item-icon"
									src="/static/imgs/home/<USER>" mode="aspectFit">
								</image>
								<image v-else-if="item.type == '3'" class="item-icon" src="/static/imgs/home/<USER>"
									mode="aspectFit">
								</image>
							</view>
						</view>
					</custom-waterfalls-flow>
				</view>
			</view>
		</view>
		<!-- 优惠券弹窗-->
		<uni-popup ref="popup" type="center" background-color="#fff" :is-mask-click="true">
			<view class="popup-content">
				<image class="popup-content-bgc" :style="{height: `${fitmentInfoObj.windowVo.imgHeight}rpx`}"
					:src="fitmentInfoObj.windowVo.imgUrl" mode="aspectFit"
					@click="acceptFn(fitmentInfoObj.windowVo.type,fitmentInfoObj.windowVo.activityType,fitmentInfoObj.windowVo.activityId)">
				</image>
				<!-- <view v-if="fitmentInfoObj.couponInfo.couponType === 2" class="popup-content-left-box">
					{{fitmentInfoObj.couponInfo.discount * 10}}<text class="popup-content-left-text">折</text>
				</view>
				<view v-else class="popup-content-left-price-box">
					<text class="popup-content-left-text">￥</text>{{fitmentInfoObj.couponInfo.money}}
				</view>
				<view class="popup-content-right-box">
					<view class="popup-content-right-text">{{fitmentInfoObj.couponInfo.name}}</view>
					<view class="popup-content-right-time">
						{{fitmentInfoObj.couponInfo.receiveStartTime}}至{{fitmentInfoObj.couponInfo.receiveEndTime}}
					</view>
				</view> -->
			</view>
		</uni-popup>
		<!-- 选择商户弹层 -->
		<uni-popup ref="merchantPopup" type="bottom" border-radius="15px 15px 0 0" :is-mask-click="false"
			background-color="#fff">
			<view class="merchant-popup-content" :catchtouchmove="true">
				<view class="merchant-popup-header-box">
					<view class="merchant-popup-header-title">选择商户</view>
					<image class="merchant-popup-header-close" src="/static/imgs/home/<USER>"
						mode="aspectFit" @click="closeMerchantPopup"></image>
				</view>
				<view class="merchant-popup-search-box">
					<view class="merchant-popup-search-main">
						<image class="merchant-popup-search-icon" src="/static/imgs/home/<USER>"
							mode="aspectFit"></image>
						<input type="text" class="merchant-popup-search-input" placeholder="请输入商户名称" v-model="queryObj.name" />
						<view class="merchant-popup-search-btn" @click="searchMerchant">搜索</view>
					</view>
				</view>
				<view class="merchant-popup-list-box">
					<view class="merchant-popup-list-main">
						<scroll-view :scroll-top="scrollTop" scroll-y="true" class="merchant-popup-list-scroll-box"
							@scrolltolower="lowerFn">
							<view class="merchant-popup-list">
								<radio-group @change="merchantRadioChange">
									<label class="merchant-item-box" v-for="(item, index) in merchantList" :key="item.id">
										<view class="merchant-item-radio">
											<radio :value="item.id" :checked="index === merchantListCurrent" activeBackgroundColor="#bdfd5b"
												iconColor="#000000" />
										</view>
										<image class="merchant-item-img" :src="item.avatar" mode="aspectFit"></image>
										<view class="merchant-item-info">
											<view class="merchant-item-name">{{item.name}}</view>
											<view class="merchant-item-address">{{item.addressDetail}}</view>
											<view class="merchant-item-distance" v-if="item.distance">{{item.distance}}米以内</view>
										</view>
									</label>
								</radio-group>
							</view>
						</scroll-view>
					</view>
				</view>
				<view class="merchant-popup-btn-box">
					<view class="merchant-popup-btn" @click="confirmMrchantFn">确定</view>
				</view>
			</view>
		</uni-popup>
		<!-- 选择门店弹层 -->
		<uni-popup ref="storePopup" type="bottom" border-radius="15px 15px 0 0" :is-mask-click="false"
			background-color="#fff">
			<view class="merchant-popup-content">
				<view class="merchant-popup-header-box">
					<view class="merchant-popup-header-title">选择门店</view>
					<image class="merchant-popup-header-close" src="/static/imgs/home/<USER>"
						mode="aspectFit" @click="closeStorePopup"></image>
				</view>
				<view class="merchant-popup-search-box">
					<view class="merchant-popup-search-main">
						<image class="merchant-popup-search-icon" src="/static/imgs/home/<USER>"
							mode="aspectFit"></image>
						<input type="text" class="merchant-popup-search-input" placeholder="请输入门店名称" v-model="storeQueryObj.name" />
						<view class="merchant-popup-search-btn" @click="searchStore">搜索</view>
					</view>
				</view>
				<view class="merchant-popup-list-box">
					<view class="merchant-popup-list-main">
						<scroll-view :scroll-top="scrollTop" scroll-y="true" class="merchant-popup-list-scroll-box"
							style="overflow: hidden;" @scrolltolower="lowerStoreFn">
							<view class="merchant-popup-list">
								<radio-group @change="storeRadioChange">
									<label class="merchant-item-box" v-for="(item, index) in storeList" :key="item.id">
										<view class="merchant-item-radio">
											<radio :value="item.id" :checked="index === storeListCurrent" activeBackgroundColor="#bdfd5b"
												iconColor="#000000" />
										</view>
										<image class="merchant-item-img" :src="item.avatar" mode="aspectFit"></image>
										<view class="merchant-item-info">
											<view class="merchant-item-name">{{item.name}}</view>
											<view class="merchant-item-address">{{item.addressDetail}}</view>
											<view class="merchant-item-distance" v-if="item.distance">{{item.distance}}米以内</view>
										</view>
									</label>
								</radio-group>
							</view>
						</scroll-view>
					</view>
				</view>
				<view class="merchant-popup-btn-box">
					<view class="merchant-popup-btn" @click="confirmStoreFn">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		type
	} from 'os';
	import {
		getFitmentInfoApi,
		getMerchantHomePageApi,
		getStoresHomePageApi,
		receiveCouponApi
	} from '@/api/home.js'

	export default {
		// computed: ,
		components: {

		},
		data() {
			return {
				statusBarHeight: 0, // 状态栏高度
				column: 2, // 瀑布流列数
				fitmentInfoObj: {}, // 首页信息
				scrollTop: 0, // 设置商户选择弹层滚动条位置
				// 商户信息分页参数
				queryObj: {
					latitude: '', // 纬度
					limit: 10, // 每页数量
					longitude: '', // 经度
					name: '', // 商户名称
					page: 1, // 页码
				},
				merchantList: [], // 商户信息分页列表
				merchantListTotal: 0, // 商户信息分页列表总数
				isMerchantListLoading: false, // 商户信息分页列表状态
				merchantListCurrent: null, // 商户信息分页列表单选配置
				storeQueryObj: {
					latitude: '', // 纬度-传经纬度时根据距离排序
					limit: 10, // 每页数量
					longitude: '', // 经度-传经纬度时根据距离排序
					merId: '', // 商户id
					name: '', // 门店名称模糊
					page: 1, // 页码
					typeList: '' //配送方式筛选集合，1配送2自提3快递
				},
				storeList: [], // 门店分页列表
				storeListTotal: 0, // 门店分页列表总数
				isStoreListLoading: false, // 门店信息分页列表状态
				storeListCurrent: null, // 门店信息分页列表单选配置
				storeId: '', // 选择的门店id
				storeName: '', // 选择的门店名称
				merchantName: '', // 选择的商户名称
				merchantAvatar: '', // 选择的商户头像
			}
		},
		watch: {

		},
		created() {
			// this.$nextTick(() => {
			// 	this.getFitmentInfo()
			// })
		},
		onLaunch: function() {
			//this.isNodes++;
		},
		mounted() {},
		onLoad(options) {
			// 获取状态栏高度
			const info = uni.getWindowInfo() // 获取设备信息
			console.log('状态栏信息', info);
			this.statusBarHeight = info.statusBarHeight
			this.getFitmentInfo()
		},
		onShow() {
			this.getFitmentInfo()
		},
		// 滚动监听
		onPageScroll(e) {

		},
		methods: {
			// 点击收下优惠券
			async acceptFn(type, activityType, activityId) {
				console.log('点击优惠券', type);
				// type 1 活动  2 优惠券
				// activityType 活动类型，1折扣 2秒杀 3新人专享 4充值
				if (type == 2) {
					console.log('跳转到领券中心');
					uni.navigateTo({
						url: '/pages/coupon/coupon_center/index'
					})
				} else {
					console.log('跳转到活动页面');
					let type = null
					if (activityType == 1) {
						type = 'newproduct'
					} else if (activityType == 2) {
						type = 'seckill'
					} else if (activityType == 3) {
						type = 'newuser'
					}
					console.log('type', type);
					uni.navigateTo({
						url: `pages/activity/index?type=${type}&activityId=${activityId}`
					})
				}
			},
			// 点击选择商店
			chooseStore() {
				console.log('选择商店');
				// uni.navigateTo({
				// 	url: '/pages/home/<USER>/merchant-home'
				// })
				this.merchantListCurrent = null
				this.merchantListTotal = 0
				this.merchantList = []

				uni.getLocation({
					type: 'wgs84',
					success: (res) => {
						console.log('当前位置的经度：' + res.longitude);
						console.log('当前位置的纬度：' + res.latitude);
						this.queryObj.latitude = res.latitude
						this.queryObj.longitude = res.longitude
						this.storeQueryObj.latitude = res.latitude
						this.storeQueryObj.longitude = res.longitude
						this.getMerchantHomePage()
						this.$refs.merchantPopup.open()
					},
					fail: (err) => {
						console.log(err);
						uni.showToast({
							icon: 'none',
							title: '请授权获取地理位置',
							duration: 2000
						});
					}
				});

			},
			// 点击顶部图片跳转链接
			goToLink(link) {
				console.log('link', link);
				uni.navigateTo({
					url: link
				})
			},
			// 点击前往搜索页面
			goSearch() {
				console.log('点击前往搜索页面');
				uni.navigateTo({
					url: `/pages/home/<USER>/search-product?merchantId=${JSON.stringify(1)}`
				})
				// this.openPopup()
				// console.log('this.$refs.CouponPopup', this.$refs.CouponPopup);
				// this.$refs.CouponPopup.open('center')
			},
			// 点击前往优惠券页面
			goToCoupon(list) {
				console.log('点击前往优惠券页面', list);
				// return
				uni.navigateTo({
					// url: `/pages/coupon/coupon_center/index?list=${JSON.stringify(list)}`
					url: `/pages/coupon/coupon_center/index`
				})
			},
			async acceptCouponFn(e) {
				console.log('点击了领取优惠券', e);
				console.log('点击了领取优惠券id', this.fitmentInfoObj);
				// const res = await receiveCouponApi(this.fitmentInfoObj.couponInfoList[0].id)
				// console.log('res', res);
				// if (res.code === 200) {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: '领取成功',
				// 		duration: 2000
				// 	});
				// } else {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: res.message,
				// 		duration: 2000
				// 	});
				// }
				receiveCouponApi(this.fitmentInfoObj.couponInfoList[0].id).then((res) => {
					uni.showToast({
						icon: 'none',
						title: '领取成功',
						duration: 2000
					});
				}).catch(err => {
					console.log('err', err);
					uni.showToast({
						icon: 'none',
						title: err,
						duration: 2000
					})
				})
			},
			// 点击前往门店首页
			goToMerchantHome(id) {
				let obj = {
					merchantId: id,
					merchantName: '',
					merchantAvatar: '',
					storeId: '',
					storeName: '',
				}
				uni.navigateTo({
					url: `/pages/home/<USER>/merchant-home?obj=${JSON.stringify(obj)}`
				})
			},
			// 瀑布流加载完成
			loaded() {
				console.log('瀑布流加载完成')
			},
			// 瀑布流单项点击事件
			wapperClick(item) {
				console.log('瀑布流单项点击事件', item);
				uni.setStorageSync('storeId', 1)
				uni.navigateTo({
					url: `/pages/goods/goods_details/index?id=${item.id}&merchantid=1`
				})
			},
			// 瀑布流图片点击事件
			imageClick(item) {
				console.log('瀑布流图片点击事件', item);
				uni.setStorageSync('storeId', 1)
				uni.navigateTo({
					url: `/pages/goods/goods_details/index?id=${item.id}&merchantid=1`
				})
			},
			// 获取首页数据
			async getFitmentInfo() {
				const res = await getFitmentInfoApi()
				if (res.code === 200) {
					this.fitmentInfoObj = res.data
					console.log('首页数据', this.fitmentInfoObj);
					if (this.fitmentInfoObj.windowVo !== null) {
						this.openPopup()
					}
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 打开优惠券弹层
			openPopup() {
				console.log('打开弹窗');
				// open 方法传入参数 等同在 uni-popup 组件上绑定 type属性
				this.$refs.popup.open()
			},
			// 获取商户信息分页
			async getMerchantHomePage() {
				this.isMerchantListLoading = true
				const res = await getMerchantHomePageApi(this.queryObj)
				if (res.code === 200) {
					this.isMerchantListLoading = false
					console.log('获取商户信息分页', res);
					this.merchantList = [...this.merchantList, ...res.data.list]
					this.merchantListTotal = res.data.total
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 点击关闭选择商户
			closeMerchantPopup() {
				console.log('关闭选择商户弹层');
				this.$refs.merchantPopup.close()
			},
			lowerFn(e) {
				console.log('滚动到底部', e)
				if (this.isMerchantListLoading) return
				this.queryObj.page++
				this.getMerchantHomePage()
				if (this.queryObj.page * this.queryObj.limit >= this.merchantListTotal) {
					return uni.showToast({
						icon: 'none',
						title: '数据加载完毕',
						duration: 2000
					});
				}
			},
			// 点击选择商户
			merchantRadioChange(evt) {
				console.log('点击选择商户evt', evt);
				this.storeQueryObj.merId = evt.detail.value
				console.log('storeQueryObj', this.storeQueryObj);
				for (let i = 0; i < this.merchantList.length; i++) {
					if (String(this.merchantList[i].id) === evt.detail.value) {
						this.merchantListCurrent = i;
						this.merchantName = this.merchantList[i].name
						this.merchantAvatar = this.merchantList[i].avatar
						// console.log('this.merchantList[i]', this.merchantList[i]);
						break;
					}
				}
			},
			// 点击搜索商户, 获取商户列表
			searchMerchant() {
				console.log('搜索', this.queryObj);
				// if (this.queryObj.name == '') {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: '请输入搜索内容',
				// 		duration: 2000
				// 	})
				// } else {
				this.merchantList = []
				this.getMerchantHomePage()
				// }
			},
			// 点击确定选择商户, 关闭选择商户弹层, 打开选择门店弹层, 并获取门店列表
			confirmMrchantFn() {
				console.log('点击了确定');
				this.$refs.merchantPopup.close()
				this.storeListCurrent = null
				this.storeListTotal = 0
				this.storeList = []
				this.$refs.storePopup.open()
				this.getStoresHomePage()
			},
			// 获取门店信息分页列表getStoresHomePageApi
			async getStoresHomePage() {
				const res = await getStoresHomePageApi(this.storeQueryObj)
				if (res.code === 200) {
					this.isStoreListLoading = false
					console.log('门店信息分页列表', res);
					this.storeList = [...this.storeList, ...res.data.list]
					this.storeListTotal = res.data.total
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 点击关闭选择门店
			closeStorePopup() {
				console.log('关闭选择门店弹层');
				this.$refs.storePopup.close()
			},
			// 点击搜索门店, 获取门店列表
			searchStore() {
				console.log('搜索门店', this.storeQueryObj);
				// if (this.queryObj.name == '') {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: '请输入搜索内容',
				// 		duration: 2000
				// 	})
				// } else {
				this.storeList = []
				this.getStoresHomePage()
				// }
			},
			// 门店列表滚动到了底部
			lowerStoreFn(e) {
				console.log('滚动到底部', e)
				if (this.isStoreListLoading) return
				this.storeQueryObj.page++
				this.getMerchantHomePage()
				if (this.storeQueryObj.page * this.storeQueryObj.limit >= this.storeListTotal) {
					return uni.showToast({
						icon: 'none',
						title: '数据加载完毕',
						duration: 2000
					});
				}
			},
			// 点击选择门店
			storeRadioChange(evt) {
				console.log('点击选择商户evt', evt);
				this.storeId = evt.detail.value
				uni.setStorageSync('storeId', this.storeId)
				for (let i = 0; i < this.storeList.length; i++) {
					if (String(this.storeList[i].id) === evt.detail.value) {
						this.storeListCurrent = i;
						this.storeName = this.storeList[i].name
						console.log('this.storeList[i]', this.storeList[i]);
						break;
					}
				}
			},
			confirmStoreFn() {
				console.log('点击了确定选择门店');
				this.$refs.storePopup.close()
				let obj = {
					merchantId: this.storeQueryObj.merId,
					merchantName: this.merchantName,
					merchantAvatar: this.merchantAvatar,
					storeId: this.storeId,
					storeName: this.storeName,
				}
				uni.navigateTo({
					// url: `/pages/home/<USER>/merchant-home?merchantId=${this.storeQueryObj.merId}&merchantName=${JSON.stringify(this.merchantName)}&merchantAvatar=${JSON.stringify(this.merchantAvatar)}&storeId=${this.storeId}&storeName=${JSON.stringify(this.storeName)}`
					url: `/pages/home/<USER>/merchant-home?obj=${JSON.stringify(obj)}`
				})
			}
		},
		mounted() {},
		onReachBottom: function() {},
		/**
		 * 用户点击右上角分享
		 */
		// #ifdef MP
		onShareAppMessage: function() {
			let spread = this.uid ? this.uid : 0;
			return {
				title: this.configApi.title,
				imageUrl: this.configApi.img,
				desc: this.configApi.synopsis,
				path: `/pages/index/index?spread=${spread}&mid=${this.currentMerId || ''}`,
			};
		}
		// #endif
	}
</script>
<style>
	page {
		/* 	height: auto;
		display: flex;
		flex-direction: column; */
		/* height: 100%; */
		/* #ifdef H5 */
		background-color: #fff;
		/* #endif */

	}
</style>
<style lang="scss" scoped>
	.home-content {
		.home-header-box {
			position: relative;
			width: 750rpx;
			height: 592rpx;

			// background-image: url('/static/imgs/home/<USER>');
			// background-position: center;
			// background-repeat: no-repeat;
			// background-size: 100 100%;
			.home-header-bgc-box {
				position: absolute;
				top: 0;
				left: 0;
				width: 750rpx;
				height: 592rpx;
			}

			.home-header-store-name-box {
				position: relative;
				display: flex;
				align-items: center;
				// margin-top: 16rpx;
				padding-left: 20rpx;
				width: 750rpx;
				height: 72rpx;
				// background-color: aqua;

				.home-header-store-icon {
					margin-right: 20rpx;
					width: 72rpx;
					height: 72rpx;
					background: #fefefe;
					border-radius: 10rpx;
				}

				.home-header-store-name-main {
					display: flex;
					align-items: center;

					.home-header-store-name-text {
						font-size: 34rpx;
						font-weight: 700;
						color: #222222;
					}

					.home-header-store-name-down-icon {
						margin-left: 8rpx;
						width: 12rpx;
						height: 12rpx;
					}
				}
			}

			.home-header-search-box {
				display: flex;
				justify-content: center;
				position: absolute;
				left: 0;
				bottom: 0;
				width: 750rpx;
				height: 76rpx;
				// background-color: #fefefe;

				.home-header-search-main {
					display: flex;
					align-items: center;
					padding: 0 20rpx;
					width: 692rpx;
					height: 76rpx;
					background: #ffffff;
					border-radius: 20rpx 20rpx 0rpx 0rpx;

					.home-header-search-icon {
						margin-right: 10rpx;
						width: 36rpx;
						height: 36rpx;
					}

					.home-header-search-input {
						flex: 1;
						font-size: 28rpx;
						color: #b2b2b2;
					}

					.home-header-search-btn {
						width: 100rpx;
						height: 56rpx;
						background: #bdfd5b;
						border-radius: 16rpx;
						text-align: center;
						line-height: 56rpx;
						color: #222222;
					}
				}
			}
		}

		.home-main-box {
			display: flex;
			justify-content: center;
			width: 750rpx;

			.home-main {
				width: 690rpx;

				.home-main-nav-box {
					// margin-bottom: 18rpx;
					padding-top: 50rpx;
					width: 690rpx;
					height: 482rpx;
					background: #ffffff;
					border-radius: 0rpx 0rpx 36rpx 36rpx;

					.nav-header-box {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 36rpx;
						padding-left: 64rpx;
						padding-right: 46rpx;
						width: 100%;

						.nav-header-item-box {
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;

							.nav-header-item-icon {
								margin-bottom: 18rpx;
								width: 80rpx;
								height: 80rpx;
								// background-color: #ffd358;
							}

							.nav-header-item-title {
								font-size: 32rpx;
								font-weight: 700;
								// text-align: center;
								color: #222222;
							}

							.nav-header-item-msg {
								font-size: 24rpx;
								// text-align: center;
								color: #999999;
							}
						}
					}

					.nav-footer-box {
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding-left: 34rpx;
						padding-right: 20rpx;
						width: 100%;

						.nav-footer-item-box {
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;

							.nav-footer-item-icon {
								margin-bottom: 18rpx;
								width: 76rpx;
								height: 76rpx;
								// background-color: #f5f5f5;
							}

							.nav-footer-item-title {
								font-size: 26rpx;
								color: #222222;
							}

							.nav-footer-item-msg {
								font-size: 22rpx;
								color: #999999;
							}
						}
					}
				}

				.home-main-newcomer-benefits-box {
					position: relative;
					// margin-bottom: 20rpx;
					width: 690rpx;
					height: 222rpx;

					.newcomer-benefits-bgc {
						width: 690rpx;
						height: 222rpx;
					}

					.newcomer-benefits-more-box {
						display: flex;
						align-items: center;
						position: absolute;
						top: 17rpx;
						right: 18rpx;

						.newcomer-benefits-more-text {
							font-size: 26rpx;
							color: #ffffff;
						}

						.newcomer-benefits-more-icon {
							width: 20rpx;
							height: 20rpx;
						}
					}

					.newcomer-benefits-price-box {
						display: flex;
						align-items: center;
						position: absolute;
						left: 30rpx;
						bottom: 38rpx;

						.newcomer-benefits-price-symbol {
							margin-top: 17rpx;
							font-size: 50rpx;
							font-weight: 700;
							color: #ff2222;
						}

						.newcomer-benefits-price-num {
							font-size: 72rpx;
							font-weight: 700;
							color: #ff2222;
						}
					}

					.newcomer-benefits-intro-box {
						position: absolute;
						top: 100rpx;
						left: 199rpx;

						.newcomer-benefits-intro-title {
							font-size: 26rpx;
							color: #ff2222;
						}

						.newcomer-benefits-intro-msg {
							font-size: 22rpx;
							color: #ff2222;
						}
					}
				}

				.home-main-merchant-recommend-box {
					// margin-bottom: 38rpx;
					width: 690rpx;

					.merchant-recommend-header-box {
						display: flex;
						align-items: center;
						margin-bottom: 42rpx;
						width: 690rpx;

						.merchant-recommend-header-line {
							width: 12rpx;
							height: 30rpx;
							background: #222222;
						}

						.merchant-recommend-header-title {
							margin-left: 6rpx;
							margin-right: 10rpx;
							font-size: 32rpx;
							font-weight: 700;
							color: #333333;
						}

						.merchant-recommend-header-msg {
							font-size: 26rpx;
							color: #b2b2b2;
						}
					}

					.merchant-recommend-list-box {
						padding: 0 21rpx;
						width: 690rpx;

						.merchant-recommend-list-main {
							width: 100%;

							.merchant-recommend-item-box {
								margin-bottom: 20rpx;
								display: flex;
								align-items: center;
								width: 100%;

								&:last-child {
									margin-bottom: 0;
								}

								.merchant-recommend-item-img {
									margin-right: 20rpx;
									width: 140rpx;
									height: 140rpx;
									background: #d8d8d8;
									border-radius: 16rpx;
								}

								.merchant-recommend-item-main {
									width: 488rpx;

									.merchant-recommend-item-name {
										margin-bottom: 12rpx;
										width: 100%;
										font-size: 28rpx;
										font-weight: 700;
										color: #333333;
										white-space: nowrap;
										overflow: hidden;
										text-overflow: ellipsis;
									}

									.merchant-recommend-item-address {
										font-size: 24rpx;
										color: #666666;
										line-clamp: 2;
										overflow: hidden;
										text-overflow: ellipsis;
										text-overflow: -webkit-ellipsis-lastline;
										display: -webkit-box;
										-webkit-line-clamp: 2;
										line-clamp: 2;
										-webkit-box-orient: vertical;
									}
								}
							}
						}
					}
				}

				.home-main-hot-recommend-box {
					width: 690rpx;

					.hot-recommend-header-box {
						margin-bottom: 30rpx;
						width: 338rpx;
						height: 48rpx;
					}

					.hot-recommend-item-box {
						width: 100%;
						padding: 26rpx 20rpx 10rpx 20rpx;

						.hot-recommend-item-name {
							margin-bottom: 10rpx;
							width: 100%;
							font-size: 28rpx;
							font-weight: 700;
							color: #333333;
							line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
							text-overflow: -webkit-ellipsis-lastline;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							-webkit-box-orient: vertical;
						}

						.hot-recommend-item-intro {
							width: 100%;
							font-size: 24rpx;
							color: rgba(102, 102, 102, 0.60);
							line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
							text-overflow: -webkit-ellipsis-lastline;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							-webkit-box-orient: vertical;
						}

						.hot-recommend-item-price-box {
							margin-top: 10rpx;
							display: flex;
							align-items: center;
							width: 100%;

							.item-price {
								margin-right: 10rpx;
								font-size: 28rpx;
								font-weight: 500;
								color: #333333;
							}

							.item-old-price {
								font-size: 28rpx;
								text-decoration: line-through;
								color: #999999;
							}

							.item-icon {
								width: 64rpx;
								height: 24rpx;
							}
						}

						.desc {
							font-size: 24rpx;
							color: #666;
						}

					}
				}
			}
		}

		.popup-content {
			display: flex;
			justify-content: center;
			width: 100%;
			position: relative;

			.popup-content-bgc {
				width: 620rpx;
				// height: 642rpx;

			}

			.popup-content-left-box {
				position: absolute;
				left: 75rpx;
				bottom: 160rpx;
				font-size: 140rpx;
				font-weight: 700;
				color: #fa4f1f;

				.popup-content-left-text {
					font-size: 52rpx;
					color: #fa4f1f;
				}
			}

			.popup-content-left-price-box {
				position: absolute;
				left: 50rpx;
				bottom: 173rpx;
				font-size: 100rpx;
				font-weight: 700;
				color: #fa4f1f;

				.popup-content-left-text {
					font-size: 52rpx;
					color: #fa4f1f;
				}
			}

			.popup-content-right-box {
				position: absolute;
				right: 120rpx;
				bottom: 190rpx;

				.popup-content-right-text {
					margin-bottom: 20rpx;
					font-size: 30rpx;
					font-weight: 500;
					color: #fa4f1f;
				}

				.popup-content-right-time {
					font-size: 20rpx;
					color: #fa4f1f;
				}
			}
		}

		.merchant-popup-content {
			padding-top: 30rpx;
			width: 100%;
			height: 75vh;

			.merchant-popup-header-box {
				display: flex;
				align-items: center;
				margin-bottom: 28rpx;
				padding-right: 26rpx;
				width: 100%;

				.merchant-popup-header-title {
					flex: 1;
					text-align: center;
					font-size: 30rpx;
					color: #222222;
				}

				.merchant-popup-header-close {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.merchant-popup-search-box {
				display: flex;
				justify-content: center;
				margin-bottom: 22rpx;
				width: 100%;

				.merchant-popup-search-main {
					display: flex;
					align-items: center;
					padding-left: 20rpx;
					padding-right: 18rpx;
					width: 710rpx;
					height: 76rpx;
					background: #f5f5f5;
					border-radius: 16rpx;

					.merchant-popup-search-icon {
						margin-right: 10rpx;
						width: 36rpx;
						height: 36rpx;
					}

					.merchant-popup-search-input {
						flex: 1;
						font-size: 28rpx;
						color: #222;
					}

					.merchant-popup-search-btn {
						margin-left: 10rpx;
						width: 100rpx;
						height: 56rpx;
						background: #bdfd5b;
						border-radius: 16rpx;
						text-align: center;
						line-height: 56rpx;
						font-size: 26rpx;
						color: #222222;
					}
				}
			}

			.merchant-popup-list-box {
				display: flex;
				justify-content: center;
				margin-bottom: 64rpx;
				width: 100%;

				.merchant-popup-list-main {
					width: 710rpx;

					.merchant-popup-list-scroll-box {
						width: 100%;
						height: 750rpx;
						// background-color: #bdfd5b;
						overflow: hidden;

						.merchant-popup-list {
							width: 100%;

							.merchant-item-box {
								display: flex;
								align-items: center;
								margin-bottom: 30rpx;
								width: 100%;

								&:last-child {
									margin-bottom: 0;
								}

								.merchant-item-radio {
									margin-right: 20rpx;
								}

								.merchant-item-img {
									margin-right: 20rpx;
									width: 140rpx;
									height: 140rpx;
									// background: #d8d8d8;
									border-radius: 16rpx;
								}

								.merchant-item-info {
									flex: 1;

									.merchant-item-name {
										margin-bottom: 12rpx;
										font-weight: 500;
										font-size: 28rpx;
										color: #333333;
										white-space: nowrap;
										overflow: hidden;
										text-overflow: ellipsis;
									}

									.merchant-item-address {
										margin-bottom: 6rpx;
										font-size: 24rpx;
										color: #666666;
										line-clamp: 2;
										overflow: hidden;
										text-overflow: ellipsis;
										text-overflow: -webkit-ellipsis-lastline;
										display: -webkit-box;
										-webkit-line-clamp: 2;
										line-clamp: 2;
										-webkit-box-orient: vertical;
									}

									.merchant-item-distance {
										font-size: 24rpx;
										text-align: right;
										color: #ff630c;
									}
								}
							}
						}

						.scroll-view-item {
							height: 300rpx;
							line-height: 300rpx;
							text-align: center;
							font-size: 36rpx;
						}
					}
				}
			}

			.merchant-popup-btn-box {
				display: flex;
				justify-content: center;
				width: 100%;

				.merchant-popup-btn {
					width: 690rpx;
					height: 72rpx;
					background: #bdfd5b;
					border-radius: 16rpx;
					text-align: center;
					line-height: 72rpx;
					font-size: 30rpx;
					color: #000000;
				}
			}
		}
	}
</style>